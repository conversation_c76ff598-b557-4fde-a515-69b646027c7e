
#include <string>

#include "ZxPro103ClientWay.h"
#include "../SecDevFlowModule.h"


CXJPro103ClientWay::CXJPro103ClientWay(CLIENT_PRO_INTERFACE& pProInterface,
									   CXJAPCIInterFace& pAPCIHandler,
									   CXJASDUInterFace& pASDUHandler,
									   CMessageLog& pLogFile,CMessageLog* pMMSLogFile,
                                       string strHome,map<int,string> mapStnDevId,string strStnMgrPt,
                                       int nDoCrcChk,int nUseDb,int nUseMMS,
                                       string strScdPathFile,
                                       CXJDBFacade * pDBAcess)
:m_rInterface(pProInterface),m_rAPCIHandler(pAPCIHandler),m_rASD<PERSON><PERSON><PERSON><PERSON>(pASDUHandler),m_rLogFile(pLogFile),m_pMMSLog(pMMSLogFile),
m_AutoUpASDULock("AutoUpASDU"),m_STTPCacheLock("STTPCache"),m_LockForCloseStn("setCloseStn"),
m_LockForRcvBusInfList("RcvStnSttpMsg"),m_LockForSendBusList("SendStnSttpMsg"),m_LockFor860Step("m_map860IedList"),
m_LockForOperationSttpList("OperationSttpMsg"),m_LockForResultSttpList("ResultSttpMsg"),
m_LockForRcvHnTcSttpList("RcvHnTcSttpMsg"),m_LockForRcvHnTcMsgList("RcvHnTcMsg"),m_LockForSendHnTcMsgList("SendHnTcMsg"),
m_LockForSend40003Sttp("Rcv40002SttpMsg"),m_LockForRcvHnTcMsgChNoList("RcvHnTcMsgChNo"),m_LockForCrc40005SttpCache("Crc40005SttpCache")
{
    m_strScdPath = strScdPathFile.c_str();
    m_nUseMMS = nUseMMS;
    if(m_nUseMMS){
        m_pCMMSAnalyze = new CMMSAnalyze();
        m_pCMMSAnalyze->Init(m_pMMSLog,pProInterface.bRecordMsg);
    }
    m_nUseDb = nUseDb;
    m_bLogDebug = pProInterface.bRecordMsg;
    m_pDBAcess = pDBAcess;
    m_pbExit = pProInterface.p_bExit;
    m_strStnId = pProInterface.pszStationId;
    
    srand((unsigned)time(NULL));
    m_tCommStatCyle = pProInterface.nAutoUpCylSstnStatus + rand() % RandCommStatCyle; //自动上送厂站通信状态周期,单位:s
    
    char c4[10];bzero(c4,sizeof(c4));
    memcpy(c4,m_strStnId.c_str(),4);
    m_strStnMgrSecDev = strStnMgrPt;
    strDragonHome = GetFormatPath( strHome );
    m_bConnected = false;
    m_bIniting = false;
    m_CommandHandleThreadId = 0;
    m_AutoUpHandleThreadId = 0;
    m_hCommandHandleThreadHandle = INVALID_THREAD;
    m_hAutoUpHandleThreadHandle = INVALID_THREAD;
    m_RcvHnTcSttpList.clear();
    m_ResultSttpList.clear();
    m_OperationSttpList.clear();
    m_mapOperThreadInfo.clear();
    m_mapResultOperThreadInfo.clear();
    m_SendBusList.clear();
    m_RcvBusInfList.clear();
	m_AutoUpASDUBuf.clear();
    bCanTcMsg = false;
    m_RcvHnTcMsgList.clear();
    m_RcvHnTcHugeMsgList.clear();
    uFrameNo = 0;
    m_bCanProxy=false;
    m_bBoot=true;
    m_bUpdatePtId = false;
    m_tStartLast = time(NULL);
    m_mapCh2DevId = mapStnDevId;
    m_mapDevIdCh2.clear();
    for(map<int,string>::iterator it=m_mapCh2DevId.begin();it!=m_mapCh2DevId.end();it++){
        m_mapDevIdCh2.insert(make_pair(it->second,it->first));
    }
    m_DoCrcChk = nDoCrcChk;
    setCloseStn.clear();
    m_mapIedName2ChNo.clear();
    m_mapCrc40005SttpCache.clear();
    m_RcvScdChg = 0;
    m_nWorkArea =2;
    m_nModFileCallType = 0;
	m_bStartCallModel = false;
}

CXJPro103ClientWay::~CXJPro103ClientWay()
{
    m_mapOperThreadWindowInfo.clear();
    if(m_nUseMMS){
        delete m_pCMMSAnalyze;
    }
}


int CXJPro103ClientWay::ReleaseResource()
{
	m_rLogFile.FormatAdd(CLogFile::trace,"104透传CXJPro103ClientWay开始释放资源");

	try{
		ReleaseAPCIHandler();	
		
		EndCommandHandleThread();		
        EndAutoUpHandleThread();
        EndResultHandleThread();
        EndResultOperThread();
        EndOperationThread();
        EndStnCfgChkThread();
        
		ReleaseASDUHandler();
		ReleaseSttpMessageCache();
        Release103CacheIndex();
        UnInitGbkU8();
	}
	catch(...)
	{
		m_rLogFile.FormatAdd(CLogFile::error,"104透传CXJPro103ClientWay释放资源时发生异常");
		return R_RUN_EXCEPTION;
	}
	
	m_rLogFile.FormatAdd(CLogFile::trace,"104透传CXJPro103ClientWay释放资源成功");
	
	return 0;	
}
void CXJPro103ClientWay::ReleaseAPCIHandler()
{
	m_rAPCIHandler.End();	
	m_rLogFile.FormatAdd(CLogFile::trace,"APCIHandler 停止服务");
}

void CXJPro103ClientWay::EndCommandHandleThread( void )
{
	//停止接收线程
	if( 0 != m_hCommandHandleThreadHandle){
		int nRet=xj_thread_join(m_hCommandHandleThreadHandle,NULL);
		if(nRet != 0){
			m_rLogFile.FormatAdd(CLogFile::error,"退出命令处理线程异常，原因为:%s",strerror(errno));
			return;
		}
	}
	m_rLogFile.FormatAdd(CLogFile::trace,"退出命令处理线程成功");	
}
void CXJPro103ClientWay::EndAutoUpHandleThread( void )
{
	if(m_hAutoUpHandleThreadHandle != 0){
		int nRet=xj_thread_join(m_hAutoUpHandleThreadHandle,NULL);
		if(nRet != 0){
			m_rLogFile.FormatAdd(CLogFile::error,"退出自动上送处理线程时发生异常，原因为:%s",strerror(errno));
			return;
		}
	}
	
	m_rLogFile.FormatAdd(CLogFile::trace,"退出自动上送处理线程成功");
}
void CXJPro103ClientWay::EndResultHandleThread( void )
{
	if(m_hResultHandleThreadHandle != 0){
		int nRet=xj_thread_join(m_hResultHandleThreadHandle,NULL);
		if(nRet != 0){
			m_rLogFile.FormatAdd(CLogFile::error,"退出结果处理线程时发生异常,原因为:%s",strerror(errno));
		}
	}
	m_rLogFile.FormatAdd(CLogFile::trace,"退出结果处理线程");	
}
void CXJPro103ClientWay::EndOperationThread( void )
{
    for(map<int,THREAD_INFO>::iterator itThreadWait=m_mapOperThreadInfo.begin(); itThreadWait!=m_mapOperThreadInfo.end(); itThreadWait++){
        if( (itThreadWait->second.ulThreadId != 0) && (itThreadWait->second.bIsRun) ){
            xj_thread_join(itThreadWait->second.ulThreadHandle,NULL);
            itThreadWait->second.ulThreadId=0;
        }
    }
}
void CXJPro103ClientWay::EndResultOperThread( void )
{
    for(map<int,THREAD_INFO>::iterator itThreadWait=m_mapResultOperThreadInfo.begin(); itThreadWait!=m_mapResultOperThreadInfo.end(); itThreadWait++){
        if( (itThreadWait->second.ulThreadId != 0) && (itThreadWait->second.bIsRun) ){
            xj_thread_join(itThreadWait->second.ulThreadHandle,NULL);
            itThreadWait->second.ulThreadId=0;
        }
    }
}
void CXJPro103ClientWay::EndStnCfgChkThread( void )
{
	if(m_StnCfgInitThreadHandle != 0){
		int nRet=xj_thread_join(m_StnCfgInitThreadHandle,NULL);
		if(nRet != 0){
			m_rLogFile.FormatAdd(CLogFile::error,"退出StnCfgChk线程异常，原因为:%s",strerror(errno));
			return;
		}
	}
	m_rLogFile.FormatAdd(CLogFile::trace,"退出StnCfgChk线程成功");	
}

void CXJPro103ClientWay::ReleaseASDUHandler()
{
	if (&m_rASDUHandler != NULL){
		m_rASDUHandler.End();	
		m_rLogFile.FormatAdd(CLogFile::trace,"ASDUHandler 停止服务");
	}
}
void CXJPro103ClientWay::ReleaseSttpMessageCache()
{
	CLockUp localLock(&m_STTPCacheLock);
	STTPMSG_CACHE_TYPE* pSttpMsgCacheNode = NULL;
	while (!m_STTPMsgCache.empty()) {
		pSttpMsgCacheNode = m_STTPMsgCache.front();
		m_STTPMsgCache.pop_front();
		if (pSttpMsgCacheNode != NULL) {
			Release103MsgCacheBySttpMsg(pSttpMsgCacheNode);
			delete pSttpMsgCacheNode;
		}
	}	
}
void CXJPro103ClientWay::Release103CacheIndex()
{
    m_index103Cache.ClearItem();
}

void CXJPro103ClientWay::SetCommandHandleThreadHandle( THREAD_HANDLE pHandle )
{
	m_hCommandHandleThreadHandle = pHandle;
}
void CXJPro103ClientWay::SetAutoUpHandleThreadHandle( THREAD_HANDLE pHandle )
{
	m_hAutoUpHandleThreadHandle = pHandle;
}
void CXJPro103ClientWay::SetResultHandleThreadHandle( THREAD_HANDLE pHandle )
{
	m_hResultHandleThreadHandle = pHandle;
}




int CXJPro103ClientWay::Run()
{
	int nRet = 0;
	// save log desciption
	char    cLog[MAX_LINE_LENGTH]="";
	
	try
	{
        nRet = StartProtocol();
	}
	catch (...)
	{
		m_rLogFile.FormatAdd(CLogFile::error,"103客户端规约库Run函数发生异常，原因为:%s",strerror(errno));
		nRet =  R_RUN_EXCEPTION;
	}
	
	
	return nRet;	
}
int CXJPro103ClientWay::StartProtocol()
{
	int nRet =0;
	
	// save log desciption
	char    cLog[MAX_LINE_LENGTH]="";

	// init memory and so on
	if( (nRet = Init()) !=0) {
		m_rLogFile.FormatAdd(CLogFile::error,"103客户端规约库初始化失败");
		return nRet;
	}

	// strat command thread
	if(!StartCommandHandleThread())
		return R_CREATE_THREAD_FAILED;
    
    if(!StartResultHandleThread())
		return R_CREATE_THREAD_FAILED;
    
	// strat AutoUp thread
	if(!StartAutoUpHandleThread())
		return R_CREATE_THREAD_FAILED;
    
    if(!StartLoadOpertionThread())
		return R_CREATE_THREAD_FAILED;
    if(!StartResultOpertionThread())
		return R_CREATE_THREAD_FAILED;
    
    if(!StartStnCfgInitThread())
		return R_CREATE_THREAD_FAILED;
    
	m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StartProtocol] 厂站[%s] flow 启动",m_strStnId.c_str(),m_rInterface.pszStationId);	

	return 0;	
}
int CXJPro103ClientWay::Init()
{
	int nRet(0);
	char    cLog[MAX_LINE_LENGTH]="";	
	
    if ((NULL == m_rInterface.pszClientName)||
        (NULL == m_rInterface.pszStationId)||
        (NULL == m_rInterface.pDestination)||
        (NULL == m_rInterface.p_bExit))
    {
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Init] 操作指针含NULL 失败",m_strStnId.c_str());
        return -1;
    }
    if( !InitGbkU8()){
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Init] InitGbkU8 失败",m_strStnId.c_str());
    }
	//初始化APCIHandler
	if ( (nRet = InitAPCIHandler()) !=0){
		m_rLogFile.FormatAdd(CLogFile::error,"初始化CXJPro103ClientWay时 初始化APCIHandler失败");
		return nRet;
	}

	//初始化ASDUHandler
	if ( (nRet = InitASDUHandler()) !=0){
		m_rLogFile.FormatAdd(CLogFile::error,"初始化CXJPro103ClientWay时 初始化ASDUHandler失败");
		return nRet;
	}
    
    
	m_rLogFile.FormatAdd(CLogFile::error,"与 %s 通讯的103客户端规约库初始化成功！",m_rInterface.pszClientName);
	return 0;	
}
void CXJPro103ClientWay::SetTimeOutByDefaultValue()
{
    bzero(&m_tTimeOut,sizeof(TIMEOUTS));
    m_tTimeOut.call_protect_settingdata = 240;
    m_tTimeOut.call_wav_settingdata = 500;
    m_tTimeOut.call_protect_comm = 60;
    m_tTimeOut.call_wav_comm = 60;
    m_tTimeOut.call_anai = 120;
    m_tTimeOut.call_switch = 120;
    m_tTimeOut.call_softboard = 120;
    m_tTimeOut.call_zone = 60;
    m_tTimeOut.call_dev_time = 120;
    m_tTimeOut.call_basic_info = 120;
    m_tTimeOut.set_setting_data = 120;
    m_tTimeOut.set_zone = 120;
    m_tTimeOut.set_soft_board = 120;
    m_tTimeOut.set_dev_time = 120;
    m_tTimeOut.set_signal_reset = 60;
    m_tTimeOut.set_wav_remote_touch = 120;
    m_tTimeOut.call_history_info = 120;
    m_tTimeOut.call_waverfile = 120;
    m_tTimeOut.call_wavfile_list = 120;
    m_tTimeOut.call_common_file = 300;
    m_tTimeOut.call_common_file_list = 300;
    m_tTimeOut.t0 = 30;
    m_tTimeOut.t1 = 15;
    m_tTimeOut.t2 = 10;
    m_tTimeOut.t3 = 20;
    m_tTimeOut.call_runstatus = 60;
    m_tTimeOut.call_single_config = 60;
}
int CXJPro103ClientWay::BbReadTimeOutConfig( CMemSet& pMemSet )
{
    if ( NULL == m_pDBAcess ) {
		m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::BbReadTimeOutConfig]数据库操作对象指针为空。",m_strStnId.c_str());
		return -1;
	}
    
	SQL_DATA pSqlData;
	pSqlData.Conditionlist.clear();
	pSqlData.Fieldlist.clear();
	
	AddField(pSqlData,"pt_setting",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"wav_setting",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"pt_comm_status",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"wav_comm_status",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"pt_ai",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"pt_di",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"pt_softboard",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"pt_zone",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"dev_time",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"dev_basic_info",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"setting_change",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"zone_change",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"softboard_change",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"verify_time",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"signale_reset",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"remote_start",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"history_event",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"wavfile",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"wavfile_list",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"commfile",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"commfile_list",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"103_t0",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"103_t1",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"103_t2",EX_STTP_DATA_TYPE_INT);
	AddField(pSqlData,"103_t3",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"run_status",EX_STTP_DATA_TYPE_INT);	
	AddField(pSqlData,"call_sconfig",EX_STTP_DATA_TYPE_INT);	
	
    char sError[255] = "";
	try
	{
		if (true == m_pDBAcess->Select(EX_STTP_INFO_FUN_TIMEOUT_CFG,pSqlData,sError,&pMemSet)) {
            if (0 != pMemSet.GetMemRowNum()) {
                return 0;
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::BbReadTimeOutConfig]select成功但是结果数量[%d]为0 异常",m_strStnId.c_str(),pMemSet.GetMemRowNum());
                return -5;
            }
		}
		else{
			m_rLogFile.FormatAdd(CLogFile::error,sError);
			return -1;
		}
	}
	catch (...)
	{
		sprintf(sError,"[%s][CTaskMngr::BbReadTimeOutConfig]读取信息时发生异常.",m_strStnId.c_str());
		m_rLogFile.FormatAdd(CLogFile::error,sError);
		return -2;
	}
	return -3;
    
}
int CXJPro103ClientWay::InitTimeOutByDb()
{
	CMemSet pMemSet;	
	try
	{
		if (BbReadTimeOutConfig(pMemSet)==0){
				pMemSet.MoveFirst();
				bzero(&m_tTimeOut,sizeof(TIMEOUTS));
				m_tTimeOut.call_protect_settingdata = atoi(pMemSet.GetValue(UINT(0)));
				m_tTimeOut.call_wav_settingdata = atoi(pMemSet.GetValue(UINT(1)));
				m_tTimeOut.call_protect_comm = atoi(pMemSet.GetValue(UINT(2)));
				m_tTimeOut.call_wav_comm = atoi(pMemSet.GetValue(UINT(3)));
				m_tTimeOut.call_anai = atoi(pMemSet.GetValue(UINT(4)));
				m_tTimeOut.call_switch = atoi(pMemSet.GetValue(UINT(5)));
				m_tTimeOut.call_softboard = atoi(pMemSet.GetValue(UINT(6)));
				m_tTimeOut.call_zone = atoi(pMemSet.GetValue(UINT(7)));
				m_tTimeOut.call_dev_time = atoi(pMemSet.GetValue(UINT(8)));
				m_tTimeOut.call_basic_info = atoi(pMemSet.GetValue(UINT(9)));
				m_tTimeOut.set_setting_data = atoi(pMemSet.GetValue(UINT(10)));
				m_tTimeOut.set_zone = atoi(pMemSet.GetValue(UINT(11)));
				m_tTimeOut.set_soft_board = atoi(pMemSet.GetValue(UINT(12)));
				m_tTimeOut.set_dev_time = atoi(pMemSet.GetValue(UINT(13)));
				m_tTimeOut.set_signal_reset = atoi(pMemSet.GetValue(UINT(14)));
				m_tTimeOut.set_wav_remote_touch = atoi(pMemSet.GetValue(UINT(15)));
				m_tTimeOut.call_history_info = atoi(pMemSet.GetValue(UINT(16)));
				m_tTimeOut.call_waverfile = atoi(pMemSet.GetValue(UINT(17)));
				m_tTimeOut.call_wavfile_list = atoi(pMemSet.GetValue(UINT(18)));
				m_tTimeOut.call_common_file = atoi(pMemSet.GetValue(UINT(19)));
				m_tTimeOut.call_common_file_list = atoi(pMemSet.GetValue(UINT(20)));
				m_tTimeOut.t0 = atoi(pMemSet.GetValue(UINT(21)));
				m_tTimeOut.t1 = atoi(pMemSet.GetValue(UINT(22)));
				m_tTimeOut.t2 = atoi(pMemSet.GetValue(UINT(23)));
				m_tTimeOut.t3 = atoi(pMemSet.GetValue(UINT(24)));
				m_tTimeOut.call_runstatus = atoi(pMemSet.GetValue(UINT(25)));
				m_tTimeOut.call_single_config = atoi(pMemSet.GetValue(UINT(26)));
				m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::InitTimeOutByDb]从数据库加载超时时间成功",m_strStnId.c_str());
		}else{
			m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::InitTimeOutByDb]从数据库加载超时时间  失败，用default值",m_strStnId.c_str());
            return -1;
		}
		
	}
	catch (...)
	{
		m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::InitTimeOutByDb]从数据库加载超时时间 异常，用default值",m_strStnId.c_str());
        return -2;
	}
	
	return 0;		
}

int CXJPro103ClientWay::InitAPCIHandler()
{
    if(m_nUseDb==1){
        int ret = InitTimeOutByDb();
        if(ret != 0){
            SetTimeOutByDefaultValue();
        }
    }else{
        SetTimeOutByDefaultValue();
    }
	TIMEOUT_103 tTimeOut;
	tTimeOut.t0 = m_tTimeOut.t0;
	tTimeOut.t1 = m_tTimeOut.t1;
	tTimeOut.t2 = m_tTimeOut.t2;
	tTimeOut.t3 = m_tTimeOut.t3;
    //tTimeOut.t1 = 30;//临时加时长解决南思测试
	m_rAPCIHandler.SetTimeOut(tTimeOut);
	m_rAPCIHandler.RegisterOnConnectCallback(OnConnectChange,this);
	m_rAPCIHandler.RegisterOnRecvASDUMsgCallback(OnRecvASDUMsg,this);	
    m_rAPCIHandler.RegisterOnSendASDUMsgFailCallback(OnSendASDUMsgFail,this);
    m_rAPCIHandler.RegisterOnSendASDUMsgSuccessCallback(OnSendASDUMsgSuccess,this);
	return m_rAPCIHandler.Start()?0:-1;
}
int CXJPro103ClientWay::InitASDUHandler()
{
	return m_rASDUHandler.Start()?0:-1;	
}


bool CXJPro103ClientWay::StartCommandHandleThread( void )
{
	// thread return code
	int nRet =0;
	
	// begine create  thread
	nRet = xj_thread_create(&m_hCommandHandleThreadHandle, &m_CommandHandleThreadId, CommandHandleThreadFunc, this);
	
	// assert create success
	if(nRet !=0)
	{
		// save log des
		m_rLogFile.FormatAdd(CLogFile::error,"创建命令处理线程失败，原因为:%s",strerror(errno));
		return false;
	}
	
	// trace 
	m_rLogFile.FormatAdd(CLogFile::trace,"创建命令处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::StartResultHandleThread( void )
{
	// thread return code
	int nRet =0;
	
	// begine create  thread
	nRet = xj_thread_create(&m_hResultHandleThreadHandle,
		&m_ResultHandleThreadId,
		ResultHandleThreadFunc,
		this);
	
	// assert create success
	if(nRet !=0)
	{
		// save log des
		m_rLogFile.FormatAdd(CLogFile::error,"创建结果处理线程失败,原因为:%s",strerror(errno));
		return false;
	}
	
	// trace 
	m_rLogFile.FormatAdd(CLogFile::trace,"创建结果处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::StartResultOpertionThread( void )
{
    for(int i=1;i<SIZEOFOPERATIONSESSION+1;i++){
        int nRet=0;	
        THREAD_INFO m_TheadInfo;
        nRet = xj_thread_create(&m_TheadInfo.ulThreadHandle,&m_TheadInfo.ulThreadId,ResultOperThreadProc,this);
        if( nRet != 0 ){
            m_TheadInfo.bIsRun = false;
            m_mapResultOperThreadInfo.insert(make_pair(i,m_TheadInfo));
            printf("[%s][CXJPro103ClientWay::StartResultOpertionThread()] [%d]ResultOperThreadProc 线程 启动时失败 原因:%s(%d)。\n",m_strStnId.c_str(),i,strerror(errno),errno);
            return false;
        }else{
            m_TheadInfo.bIsRun = true;
            m_mapResultOperThreadInfo.insert(make_pair(i,m_TheadInfo));
            printf("[%s][CXJPro103ClientWay::StartResultOpertionThread()] [%d]ResultOperThreadProc 线程ID[%ld] 启动成功。\n",m_strStnId.c_str(),i,m_TheadInfo.ulThreadId);
        }
    }
	m_rLogFile.FormatAdd(CLogFile::trace,"创建 ResultOperThreadProc 处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::StartLoadOpertionThread( void )
{
    for(int i=1;i<SIZEOFOPERATIONSESSION+1;i++){
        int nRet=0;	
        THREAD_INFO m_TheadInfo;
        nRet = xj_thread_create(&m_TheadInfo.ulThreadHandle,&m_TheadInfo.ulThreadId,OperationThreadProc,this);
        if( nRet != 0 ){
            m_TheadInfo.bIsRun = false;
            m_mapOperThreadInfo.insert(make_pair(i,m_TheadInfo));
            printf("[%s][CXJPro103ClientWay::StartLoadOpertionThread()] [%d]OperationThreadProc 线程 启动时失败 原因:%s(%d)。\n",m_strStnId.c_str(),i,strerror(errno),errno);
            return false;
        }else{
            m_TheadInfo.bIsRun = true;
            m_mapOperThreadInfo.insert(make_pair(i,m_TheadInfo));
            m_mapOperThreadWindowInfo.insert(make_pair(m_TheadInfo.ulThreadId,m_TheadInfo));
            printf("[%s][CXJPro103ClientWay::StartLoadOpertionThread()] [%d]OperationThreadProc 线程ID[%ld] 启动成功。\n",m_strStnId.c_str(),i,m_TheadInfo.ulThreadId);
        }
    }
	m_rLogFile.FormatAdd(CLogFile::trace,"创建LoadOpertion处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::StartAutoUpHandleThread( void )
{
	// thread return code
	int nRet =0;
	
	// begine create  thread
	nRet = xj_thread_create(&m_hAutoUpHandleThreadHandle,
		&m_AutoUpHandleThreadId,
		AutoUpHandleThreadFunc,
		this);
	
	// assert create success
	if(nRet !=0)
	{
		// save log des
		m_rLogFile.FormatAdd(CLogFile::error,"创建自动上送处理线程失败，原因为:%s",strerror(errno));
		return false;
	}
	
	// trace 
	m_rLogFile.FormatAdd(CLogFile::trace,"创建自动上送处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::StartStnCfgInitThread( void )
{
	int nRet =0;
	nRet = xj_thread_create(&m_StnCfgInitThreadHandle, &m_StnCfgInitThreadId,StnCfgInitThreadFunc,this);
	if(nRet !=0) {
		// save log des
		m_rLogFile.FormatAdd(CLogFile::error,"创建命令处理线程失败，原因为:%s",strerror(errno));
		return false;
	}
	m_rLogFile.FormatAdd(CLogFile::trace,"创建命令处理线程成功");
	
	return true;	
}
bool CXJPro103ClientWay::IfExit()
{
    if (NULL != m_pbExit)
    {
        return *m_pbExit;
    } 
    else
    {
        return true;
    }

}


THREAD_FUNC WINAPI CXJPro103ClientWay::CommandHandleThreadFunc( LPVOID pParam )
{
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
	//pthread_t ulMyThreadId = pthread_self();
    
    while(!pThis->IfExit())
    {
        int nRet =0;
            nRet=pThis->CommandHandleLoop();
            if(nRet!=0) {
                pThis->m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::CommandHandleLoop()] [%s] 异常返回[%d]",pThis->m_strStnId.c_str(),nRet);
            }
            MySleep(1);
    }
	
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]sttp处理线程exit,exception,error[%s]code[%d] [%s] [%d]",pThis->m_strStnId.c_str(), __FUNCTION__,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}
THREAD_FUNC WINAPI CXJPro103ClientWay::ResultHandleThreadFunc( LPVOID pParam )
{
	// save log des
	int nRet =0;
	
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
	nRet = pThis->ResultHandleLoop();  
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]结果回复处理线程exit ret[%d]exception,error[%s]code=[%d] [%s] [%d]",pThis->m_strStnId.c_str(),__FUNCTION__,nRet,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}
THREAD_FUNC WINAPI CXJPro103ClientWay::AutoUpHandleThreadFunc( LPVOID pParam )
{
	int nRet = 0;
	
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
	nRet = pThis->AutoUpHandleLoop();

	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]主动上送处理线程exit ret[%d]exception,error[%s]code=[%d] [%s] [%d]",pThis->m_strStnId.c_str(),__FUNCTION__,nRet,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}
THREAD_FUNC WINAPI CXJPro103ClientWay::OperationThreadProc(LPVOID pParam)
{
	// get the session pointer
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    while(!pThis->IfExit())
    {
        pthread_t ulMyThreadId = pthread_self();
        int nRet =0;
        nRet=pThis->LoadOperationLoop(ulMyThreadId);
        if(nRet!=0) {
            pThis->m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::LoadOperationLoop()] [%s] 异常返回[%d]",pThis->m_strStnId.c_str(),nRet);
        }
        MySleep(1);
    }
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]下装处理池线程exit,exception,error[%s]code=[%d] [%s] [%d]",pThis->m_strStnId.c_str(),__FUNCTION__,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}
THREAD_FUNC WINAPI CXJPro103ClientWay::ResultOperThreadProc(LPVOID pParam)
{
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    while(!pThis->IfExit())
    {
        int nRet =0;
        nRet=pThis->LoadResultOperLoop();
        if(nRet!=0) {
            pThis->m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::LoadResultOperLoop()] [%s] 异常返回[%d]",pThis->m_strStnId.c_str(),nRet);
        }
        MySleep(1);
    }
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]回复处理池线程exit,exception,error[%s]code=[%d] [%s] [%d]",pThis->m_strStnId.c_str(),__FUNCTION__,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}
THREAD_FUNC WINAPI CXJPro103ClientWay::StnCfgInitThreadFunc( LPVOID pParam )
{
	CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    while(!pThis->IfExit())
    {
        int nRet =0;
        nRet=pThis->StnCfgInitLoop();
        if(nRet!=0) {
            pThis->m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitThreadFunc()] [%s] 异常返回[%d]",pThis->m_strStnId.c_str(),nRet);
        }
        MySleep(1);
    }
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][%s]厂站调度线程exit,error[%s]code=[%d] [%s] [%d]",pThis->m_strStnId.c_str(),__FUNCTION__,strerror(errno),errno,__FILE__,__LINE__);
	return THREAD_RETURN;
}




//crc检查，厂站模型管理-------------------------------------------------------
int CXJPro103ClientWay::StnCfgInitLoop()
{   
    time_t tNow = time(NULL);
    //断连复位-无效，只能收到up的时候的回调change
    if(!m_bConnected){
        if(m_bBoot==false) {//断连相当于复位程序
            m_bBoot=true;
            m_bCanProxy = false;
            STN_CFG_INFO s1;   sStnCfgInfoFile=s1;
            STN_MODEL_INFO s2; sStnMdlInfoFile=s2;
            {
                CLockUp lockUp(&m_LockFor860Step);
                m_map860IedList.clear();
            }
            {//上前置的缓冲区
                CLockUp lockUp(&m_LockForSendHnTcMsgList);
                m_mapNoTcMsg.clear();
            }
            {//下设备的缓冲区
                CLockUp lockUp(&m_LockForRcvHnTcMsgChNoList);
                m_mapRcvHnTcMsg.clear();
            }
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] OnConnectChange  断连复位 ",m_strStnId.c_str());
        }
        CLockUp lockUp(&m_LockForCloseStn);
        setCloseStn.insert(m_strStnId);//一上来就连不上，要不停拒绝前置的链接
        return 0;
    }
    
    static bool bFirstBoot=true;
    
    //第一次连上子站自动下载，以后靠上送的scd变化才下载
    if(bFirstBoot){
        string strScdInfo = strDragonHome+m_strStnId+m_strScdPath;
        CallAutoScdFile();
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]程序第一次启动召唤scd",m_strStnId.c_str(),strScdInfo.c_str());
        bFirstBoot = false;
    }
    
    //启动，照crc，招或加载文件
    if(m_bBoot){
        m_bBoot = false;
        string strCfgInfo = strDragonHome+m_strStnId+"/CONFIG/config.info";
        CallCfgInfoFile();
        m_bUpdatePtId = true;
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]boot召唤.m_bUpdatePtId[%d]",m_strStnId.c_str(),strCfgInfo.c_str(),m_bUpdatePtId);
       
        if(m_nModFileCallType == 0)
        {
            string strMdlInfo = strDragonHome+m_strStnId+"/MODEL/model.xml";
            CallModelXmlFile();
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]模型文件召读",m_strStnId.c_str(),strMdlInfo.c_str());  
        }
        else
        {
            CallModelFileList();// 20240606  lmy add 召读模型文件列表
			m_bStartCallModel = true;
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件模型文件列表召读",m_strStnId.c_str());  
        }
    }
    
    //周期招状态
    if( (tNow - m_tStartLast) > m_tCommStatCyle ){
        //发送20002，防止前置起晚了没收到
        //SendAllPtChNo20002();
        //改为一起发，避免数据量大导致总线卡
//        for( map<int,STEP_INFO>::iterator itNo=m_map860IedList.begin();itNo!=m_map860IedList.end();itNo++ ){
//            int nCh = itNo->first;
//            int nStatus = 2;//0-正常，1-停运,2-未知
//            if(itNo->second.nStep==RcvInitOk){
//                nStatus = 0;
//            }else if(itNo->second.nStep==LinkUnknow){
//                nStatus = 2;
//            }else{
//                nStatus = 1;
//            }
//            SendPtChNo20002(nCh,nStatus);
//        }
        
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] [%d]s周期到，开始检查厂站 conn[%d]",m_strStnId.c_str(),m_tCommStatCyle,m_bConnected);
        //周期招条目值，后续发通讯状态变化或入库
        if( sStnMdlInfoFile.bChkCrcOk ){
            for(map<STN_MODEL_GROUP,  map<string,STN_MODEL_ITEM > >::iterator it=sStnMdlInfoFile.mapInfo.begin();it!=sStnMdlInfoFile.mapInfo.end();it++){
                uint8 uGrp = atoi(it->first.strId.c_str());
                if( (uGrp==1)||(uGrp==2) ){
                    CallAsdu200GrpItemOneGrpItems(uGrp);
                }
            }
        }
        //crc不过就不重试了，告警了已经
//        if(!m_bCanProxy){
//            CallAsdu200Crc();
//        }
        m_tStartLast = tNow;
        {
            CLockUp lockUp(&m_LockForRcvHnTcMsgList);
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] [%d]s周期到，tpkt包内容下设备m_RcvHnTcMsgList[%d] ",m_strStnId.c_str(),
                m_tCommStatCyle,m_RcvHnTcMsgList.size());
        }
    
    }
    
    //40005-40006cache超时检查
    {
        CLockUp lockUp1(&m_LockForCrc40005SttpCache);
        map<int,RCV_40005_INFO >::iterator it=m_mapCrc40005SttpCache.begin();
        while(it!=m_mapCrc40005SttpCache.end()){
            if( (tNow - it->second.tRcvTime)>m_tTimeOut.call_switch ){
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 通道号[%d] 有40005 rcv[%d]超时[%d]未回复,回复失败40006,复位该通道,删除缓存",
                            m_strStnId.c_str(),it->first,it->second.tRcvTime,m_tTimeOut.call_switch);
                string strMsg="超时未回复";
                time_t tRcv = it->second.s40005Sttp.t_time;
                int nRii = it->second.s40005Sttp.sttp_data.sttp_head.uMsgRii;
                string strPtId = it->second.s40005Sttp.sttp_data.sttp_body.ch_pt_id;
                string strIedName = it->second.s40005Sttp.sttp_data.sttp_body.ch_HandlerName;
                string strRef = it->second.s40005Sttp.sttp_data.sttp_body.ch_version;
                Send40006Sttp(nRii,0,strMsg,m_strStnId,strPtId,strIedName,strRef);
                ResetOneIedStep(it->first);
                m_mapCrc40005SttpCache.erase(it++);
            }else{
                ++it;
            }
        }
    }
    
    //收到子站返回后逻辑
    if(!m_RcvHnTcSttpList.empty()){
        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
        {
            CLockUp lockUp(&m_LockForRcvHnTcSttpList);//锁，出括号自己释放
            Sttp_Info = m_RcvHnTcSttpList.front();
            m_RcvHnTcSttpList.pop_front();
        }

        int uMsgID = Sttp_Info.sttp_data.sttp_head.uMsgID;
        switch( uMsgID )
        {
        case 212:	   //file，只有这三类自动召唤的才会copy一份在这里处理，其他自动sttp出去了
            { 
                string strFileName = Sttp_Info.sttp_data.sttp_body.variant_member.file_data.strFileName.c_str();
               m_rLogFile.FormatAdd(CLogFile::trace,"[%s]rcv---212--strFileName[%s]",m_strStnId.c_str(),strFileName.c_str());
                if(strstr(strFileName.c_str(),"/CONFIG/config.info")){
                    PraserCfgInfoFile(strFileName);
                    if(m_bUpdatePtId){
                        m_bUpdatePtId = false;//只有配置生效 或者 第一次进来 或者 断连重连 才按新config更新ptid
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]rcv---212--strFileName[%s]做清0后m_bUpdatePtId[%d],m_nUseDb[%d]",m_strStnId.c_str(),strFileName.c_str(),m_bUpdatePtId,m_nUseDb);
                        if( (m_nUseDb==1) && (m_pDBAcess!=NULL) ){
                            DbGetCh2PtByConfig();
                            DbGetLd2IedName();
                        }
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]rcv---212--strFileName[%s]m_bUpdatePtId[%d]不做",m_strStnId.c_str(),strFileName.c_str(),m_bUpdatePtId);
                    }
                    sStnCfgInfoFile.bFileRcv=true;
                    if(sStnCfgInfoFile.bFileRcv && sStnMdlInfoFile.bFileRcv){
                        CallAsdu200Crc();
                    }
                }
                //if(strstr(strFileName.c_str(),"/MODEL/model.xml")){
				if(strstr(strFileName.c_str(),"/MODEL/")){
                    PraserModelXmlFile(strFileName);
                    sStnMdlInfoFile.bFileRcv=true;
                    if(sStnCfgInfoFile.bFileRcv && sStnMdlInfoFile.bFileRcv){
                        CallAsdu200Crc();
                    }
                }
                if(strstr(strFileName.c_str(),m_strScdPath.c_str())){
                    SendFile20531(Sttp_Info.sttp_data,Normal20531);//自动召唤scd发20531，手动的不用
                    if(m_RcvScdChg>0){
                        m_RcvScdChg--;
                        STTP_FULL_DATA sNull;
                        SendFile20531(sNull,RcvScdChg20531);
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]scd变化 子站上送的通知 等待文件完成[%d]，已完成，发特殊的20531给外部用户",m_strStnId.c_str(),m_strScdPath.c_str(),m_RcvScdChg);                 
                    }
                }
                if(m_DoCrcChk==0){
                    if(sStnCfgInfoFile.bFileRcv){
                        m_bCanProxy = true;
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]212--step-sStnCfgInfoFile.bFileRcv[%d]m_bCanProxy[%d]",m_strStnId.c_str(),sStnCfgInfoFile.bFileRcv,m_bCanProxy);
                    }
                }
            }
            break;
        case 40002:	   //透传回复
            {
                int nCmd = Sttp_Info.sttp_data.sttp_body.nEventType;//
                
               m_rLogFile.FormatAdd(CLogFile::trace,"-[%s]---------rcv 40002[%d][0-con1-close2-data]数据长度%d",m_strStnId.c_str(),nCmd,Sttp_Info.sttp_data.sttp_body.strMessage.length());
               m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d] ，命令类型%d, 规约类型[%d], 结果类型%d",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,nCmd,Sttp_Info.sttp_data.sttp_body.nSource,Sttp_Info.sttp_data.sttp_body.nChangeInfoType);
                if(nCmd==CmdProxyPacket){
                    int nType = Sttp_Info.sttp_data.sttp_body.nCmdSource;
                    if(nType == ParketRepVersion){//crc返回
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]-CRC-rcv crc返回",m_strStnId.c_str());
                        Read40002Ver(Sttp_Info.sttp_data);
                        CmpCrcAndFileVal();
                        if( (!sStnCfgInfoFile.bChkCrcOk) || (!sStnMdlInfoFile.bChkCrcOk) ){
                            if(m_DoCrcChk){
                                SendAlarmStnCrcFail();//告警的后处理人工介入，考虑招文件后触发crc重招和比较，不用重启程序
                            }
                        }else{
                            for(map<STN_MODEL_GROUP,  map<string,STN_MODEL_ITEM > >::iterator it=sStnMdlInfoFile.mapInfo.begin();it!=sStnMdlInfoFile.mapInfo.end();it++){
                                uint8 uGrp = atoi(it->first.strId.c_str());
                                CallAsdu200GrpItemOneGrpItems(uGrp);
                            }
                        }
                    }else
                    if(nType == ParketRepItem){
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] rcv 条目返回",m_strStnId.c_str());
                        Read40002Item(Sttp_Info.sttp_data);//发sttp用
                    }else
                    if(nType == ParketAutoUpItem){
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] ParketAutoUpItem[%d]nType 检查复位状态",m_strStnId.c_str(),nType); 
                        Read40002ItemUp(Sttp_Info.sttp_data);//在上送线程里已处理
                    }else
                    if(nType == ParketProxy){
                       
			            // lmy add   透传数据包
			            //if (Sttp_Info.sttp_data.sttp_body.nSource>1 && Sttp_Info.sttp_data.sttp_body.nChangeInfoType==0) //  1 :1:DL/T860 其它为103
                        //if (Sttp_Info.sttp_data.sttp_body.nSource>1 && 1!= Sttp_Info.sttp_data.sttp_body.nChangeInfoType)
                        if (Sttp_Info.sttp_data.sttp_body.nSource>1 && Sttp_Info.sttp_data.sttp_body.strMessage.size()>0)
			            {
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d] ，规约类型[%d],SecDevFlowMoudle, 结果类型%d",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,Sttp_Info.sttp_data.sttp_body.nSource,Sttp_Info.sttp_data.sttp_body.nChangeInfoType);
                            PrintBytes(Sttp_Info.sttp_data.sttp_body.strMessage );
                            if (1 == Sttp_Info.sttp_data.sttp_body.nChangeInfoType)  //发送透传包数据包失败，通道置为关闭状态
                            { 
                                SecDevFlowMoudle::getInstanse()->setStep(m_strStnId, Sttp_Info.sttp_data.sttp_body.nCpu, 1);
                            }
                            else
                            {
                                SecDevFlowMoudle::getInstanse()->setStep(m_strStnId, Sttp_Info.sttp_data.sttp_body.nCpu, nCmd); //PROXY_PACKET
                                SecDevFlowMoudle::getInstanse()->addRecvMsg(m_strStnId, Sttp_Info.sttp_data.sttp_body.nCpu, Sttp_Info.sttp_data.sttp_body.strMessage);
                            }
                            break;
			            }
                        
                       if( checkAbort(Sttp_Info.sttp_data.sttp_body.strMessage,Sttp_Info.sttp_data.sttp_body.nCpu)) break;  // 20231118 lmy add 设备中断处理
                        
                        CLockUp lockUp(&m_LockFor860Step);
                        map<int,STEP_INFO>::iterator it1=m_map860IedList.find(Sttp_Info.sttp_data.sttp_body.nCpu);
                        if(it1!=m_map860IedList.end()){
                            PrintBytes(Sttp_Info.sttp_data.sttp_body.strMessage );
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv MMS len[%d] ied step now[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,Sttp_Info.sttp_data.sttp_body.strMessage.length(),it1->second.nStep);
                            //CR的CC返回，下一步
                            if(it1->second.nStep==WtReCC){
                                int r = ChkAsdu200CC(Sttp_Info.sttp_data.sttp_body.strMessage);
                                if(r==0){
                                    if(it1->second.nStep==WtReCC){
                                        it1->second.nStep = RcvCC;
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]收到CC数据包，rcv CC step next[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep);
                                    }else{
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv CC step err[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep);
                                    }
                                }else{
                                    it1->second.nStep = LinkUnknow;
                                    it1->second.nFailCount = (it1->second.nFailCount>65535) ? 65535 : (it1->second.nFailCount+1);
                                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv not CC step back next[%d] nFailCount++[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep,it1->second.nFailCount);
                                }
                            }else
                            if(it1->second.nStep==WtReInit){
                            //init-req的Init-Rsp返回，下一步
                                int r = ChkAsdu200Init(Sttp_Info.sttp_data.sttp_body.strMessage);
                                if(r==0){
                                    if(it1->second.nStep==WtReInit){
                                        it1->second.nStep = RcvInitOk;
                                        it1->second.nFailCount = 0;//init成功清零
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]收到INIT数据包，rcv init step next[%d] step-------MMS------------------------假装init完成",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep);
                                       SendPtChNo20002(Sttp_Info.sttp_data.sttp_body.nCpu,0);//这个可以，因为这个次数少
                                       //通知前置可以开始发送使能
                                       SendPtChNo40007(Sttp_Info.sttp_data.sttp_body.nCpu);
                                    }else{
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv init step err[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep);
                                    }
                                }else{
                                    it1->second.nStep = RcvCC;
                                    it1->second.nFailCount = (it1->second.nFailCount>65535) ? 65535 : (it1->second.nFailCount+1);
                                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv not init step back next[%d] nFailCount++[%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep,it1->second.nFailCount);
                                }
                            }else
                            if(it1->second.nStep==RcvInitOk){
                            //可以透传的话//含ied则透传
                                //int r = ChkAsdu200DT(Sttp_Info.sttp_data.sttp_body.strMessage,it1->second.strIedName);
                                //if(r==0){
                                    m_rLogFile.FormatAdd(CLogFile::trace,"-[%s]---------rcv 40002[%d][0-con1-close2-data] time 收到子站上送包内容",m_strStnId.c_str(),nCmd);
                                    //PrintBytes(Sttp_Info.sttp_data.sttp_body.strMessage);
                                    CLockUp lockUp(&m_LockForSendHnTcMsgList);
                                    map<int,CHNO_TC_MSG >::iterator itNo = m_mapNoTcMsg.find(Sttp_Info.sttp_data.sttp_body.nCpu);
                                    if(itNo != m_mapNoTcMsg.end()){
                                        itNo->second.vFullMsg.insert(itNo->second.vFullMsg.end(),Sttp_Info.sttp_data.sttp_body.strMessage.begin(),Sttp_Info.sttp_data.sttp_body.strMessage.end());
                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]收到DT常规数据包，---------rcv MMS data step now[%d] get ied[%s] msglen[%d]can up to ZcsFront 该通道流vector[%d] time",m_strStnId.c_str(),
                                                Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep, it1->second.strIedName.c_str(),Sttp_Info.sttp_data.sttp_body.strMessage.length(),itNo->second.vFullMsg.size());
                                    }else{
                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]收到DT常规数据包，---------rcv MMS data step now[%d] get ied[%s] msglen[%d]can up 找不到通道号 NOT to ZcsFront 该通道流vector[%d]",m_strStnId.c_str(),
                                                Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep, it1->second.strIedName.c_str(),Sttp_Info.sttp_data.sttp_body.strMessage.length(),itNo->second.vFullMsg.size());
                                    }
//                                        m_SendHnTcMsgList.push_back(Sttp_Info.sttp_data.sttp_body.strMessage);
//                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]---------rcv MMS data step now[%d] get ied[%s] msglen[%d]can up to ZcsFront m_SendHnTcMsgList [%d]",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,it1->second.nStep, it1->second.strIedName.c_str(),Sttp_Info.sttp_data.sttp_body.strMessage.length(),m_SendHnTcMsgList.size());
                                    
                                    //}else{
                                //   m_rLogFile.FormatAdd(CLogFile::trace,"rcv data step now[%d] not ied[%s] can not proxy",it1->second.nStep, it1->second.strIedName.c_str());
                                //}
                            }
                            //其他包静默
                           m_rLogFile.FormatAdd(CLogFile::trace,"----------rcv ParketProxy[%d]",it1->second.nStep);
                        }else{
                           m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]rcv MMS len[%d] not find ied",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu,Sttp_Info.sttp_data.sttp_body.strMessage.length());
                        }
                    }else
                    if(nType == ParketAutoUpCfgChg){
                       m_rLogFile.FormatAdd(CLogFile::trace,"rcv 配置生效 上送");
                        //考虑断dev连+reboot
                        m_bBoot=true;
                        m_bCanProxy = false;
                        STN_CFG_INFO s1;   sStnCfgInfoFile=s1;
                        STN_MODEL_INFO s2; sStnMdlInfoFile=s2;
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]40002 子站 上送 配置变化 reboot",m_strStnId.c_str());
                        CLockUp lockUp(&m_LockForCloseStn);
                        setCloseStn.insert(m_strStnId);
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::OnConnectChange()] step 40002 子站 上送 配置变化 reboot  断连复位 准备关闭该站前置",m_strStnId.c_str());
                    }else
                    if(nType == ParketAutoUpScdChg){
                        m_rLogFile.FormatAdd(CLogFile::trace,"rcv scd变化 上送");
                        //单独发个20531给凯默用，通知收到子站上送的scd变化
                        m_RcvScdChg++;
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]scd变化 子站上送的通知 等待文件完成后[%d]转发 特殊的20531给外部用户",m_strStnId.c_str(),m_strScdPath.c_str(),m_RcvScdChg);
                        //自动召唤标准scd文件
                        string strScdInfo = strDragonHome+m_strStnId+m_strScdPath;
                        CallAutoScdFile();
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop]文件[%s]scd变化 召唤",m_strStnId.c_str(),strScdInfo.c_str());
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] [%d]nType not find",m_strStnId.c_str(),nType); 
                    }
                }else 
                if(nCmd==CmdEstablishConnet){
                    CLockUp lockUp(&m_LockFor860Step);
					// lmy add   建立链接
					if (Sttp_Info.sttp_data.sttp_body.nSource!=1 && Sttp_Info.sttp_data.sttp_body.nChangeInfoType==0) //  1 :1:DL/T860 其它为103, nChangeInfoType结果（RST: 0 成功, 1 失败）
					{
								m_rLogFile.FormatAdd(CLogFile::trace,"通道号[%d],建立链接",Sttp_Info.sttp_data.sttp_body.nCpu);
								SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,Sttp_Info.sttp_data.sttp_body.nCpu,nCmd); // nCmd: CREATE_CONNECT
					}
					else if(1!=Sttp_Info.sttp_data.sttp_body.nSource  && 1==Sttp_Info.sttp_data.sttp_body.nChangeInfoType)  // nChangeInfoType rst 结果 1：失败 ,0 成功
					{
						// 2024 / 01/ 22 蒋磊提出，(南自设备 ，设备为TCP客户端，子站不会主动建连，收到子站后建连失败关闭连接）
						SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,Sttp_Info.sttp_data.sttp_body.nCpu,1); 
					   // m_rLogFile.FormatAdd(CLogFile::trace,"通道号[%d] 关闭连接,2024 / 01/ 22 蒋磊提出，(南自设备 ，设备为TCP客户端，子站不会主动建连，收到子站后建连失败关闭连接）",Sttp_Info.sttp_data.sttp_body.nCpu);
					}  
              
                    map<int,STEP_INFO>::iterator it1=m_map860IedList.find(Sttp_Info.sttp_data.sttp_body.nCpu);
                    if(it1!=m_map860IedList.end()){
                        if(Sttp_Info.sttp_data.sttp_body.nChangeInfoType==0){//CMP<0>:=成功
                            if(it1->second.nStep==LinkWtConn){
                                it1->second.nStep = LinkConned;
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn ok step [%d]",it1->second.nStep);
                            }else{
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn ok step err[%d]",it1->second.nStep);
                            }
                        }else 
                        if(Sttp_Info.sttp_data.sttp_body.nChangeInfoType==1){//CMP<1>:=失败
                            if(it1->second.nStep==LinkWtConn){
                                it1->second.nStep = LinkClosed;
                                it1->second.nFailCount = (it1->second.nFailCount>65535) ? 65535 : (it1->second.nFailCount+1);
                                m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn fail step [%d] nFailCount++[%d]",it1->second.nStep,it1->second.nFailCount);
                            }else{
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn fail step err[%d]",it1->second.nStep);
                            }
                        }
                       m_rLogFile.FormatAdd(CLogFile::trace,"-[%s]---------rcv CmdEstablishConnet 40002 通道号[%d] 收到链接已建立结果[%d][0-成功1-失败] step[%d] nFailCount[%d]",m_strStnId.c_str(),
                               it1->first,Sttp_Info.sttp_data.sttp_body.nChangeInfoType,it1->second.nStep,it1->second.nFailCount);
                       if (0 == Sttp_Info.sttp_data.sttp_body.nChangeInfoType)
                       {
                           time_t tNow = time(NULL);
                           it1->second.tStepNextTime = tNow;
                           it1->second.tStepCur = tNow;
                       }


                    }   
                    
                }else 
                if(nCmd==CmdCloseConnect){
                    CLockUp lockUp(&m_LockFor860Step);
					// lmy add  关闭链接
					if (Sttp_Info.sttp_data.sttp_body.nSource!=1) //  1 :1:DL/T860 其它为103
					{
						m_rLogFile.FormatAdd(CLogFile::trace,"通道号[%d],关闭连接",Sttp_Info.sttp_data.sttp_body.nCpu);
								SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,Sttp_Info.sttp_data.sttp_body.nCpu,nCmd); 
					}
					m_rLogFile.FormatAdd(CLogFile::trace,"[stn:%s] [%d ] 关闭连接",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.nCpu);

                    map<int,STEP_INFO>::iterator it1=m_map860IedList.find(Sttp_Info.sttp_data.sttp_body.nCpu);
                    if(it1!=m_map860IedList.end()){
                        if(Sttp_Info.sttp_data.sttp_body.nChangeInfoType==0){//CMP<0>:=成功
                            if(it1->second.nStep==LinkWtClose){
                                it1->second.nStep = LinkClosed;
                                SendPtChNo20002(it1->first,1);//放这里，因为step往前进数量少，不像状态机里数量多。
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn close ok step to[%d]",it1->second.nStep);
                            }else{
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn close ok step err[%d]",it1->second.nStep);
                            }
                        }else 
                        if(Sttp_Info.sttp_data.sttp_body.nChangeInfoType==1){//CMP<1>:=失败
                            if(it1->second.nStep==LinkWtClose){
                                it1->second.nStep = LinkUnknow;//假如通的时候我手动关，关失败了，我不可能当close，必须当unknow
                                it1->second.nFailCount = (it1->second.nFailCount>65535) ? 65535 : (it1->second.nFailCount+1);
                                //比如未conn的close可能回失败，可能会不停反复这步,
                                //但是Unknow->con->close也不合理，万一前面已con，
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn close fail step to[%d] nFailCount++[%d]",it1->second.nStep,it1->second.nFailCount);
                            }else{
                               m_rLogFile.FormatAdd(CLogFile::trace,"rcv conn close fail step err[%d]",it1->second.nStep);
                            }
                        }
                        m_rLogFile.FormatAdd(CLogFile::trace,"-[%s]---------rcv CmdCloseConnect 40002 通道号[%d]收到链接已关闭结果[%d][0-成功1-失败] step[%d] nFailCount[%d]",m_strStnId.c_str(),it1->first,Sttp_Info.sttp_data.sttp_body.nChangeInfoType,it1->second.nStep,it1->second.nFailCount);
                    }
                }
            }
            break;
        case 40004:	   //配置生效结果
            {
                m_rLogFile.FormatAdd(CLogFile::trace,"-[%s]---------rcv 40004,装置ID:%s, 数据长度%d",m_strStnId.c_str(),Sttp_Info.sttp_data.sttp_body.ch_pt_id,Sttp_Info.sttp_data.sttp_body.strMessage.length());
                if(Sttp_Info.sttp_data.sttp_body.ch_pt_id)
                //ParketRepCfgEnable
                break;
            }
        case 20165:	   //刷新模型
            {
                //config理论上没变不重新解析
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s]收到模型变化，20165，准备更新pt和ied关系，m_bUpdatePtId[%d],m_nUseDb[%d]",m_strStnId.c_str(),m_bUpdatePtId,m_nUseDb);
                if( (m_nUseDb==1) && (m_pDBAcess!=NULL) ){
                    DbGetCh2PtByConfig();
                    DbGetLd2IedName();
                }
                break;
            }
        case 40005:	//招软件版本
            {
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 40005 uMsgID[%d] rii[%d] pt[%s]ied[%s]flag[%d]参引[%s]",m_strStnId.c_str(),
                            Sttp_Info.sttp_data.sttp_head.uMsgID, Sttp_Info.sttp_data.sttp_head.uMsgRii,
                            Sttp_Info.sttp_data.sttp_body.ch_pt_id, Sttp_Info.sttp_data.sttp_body.ch_HandlerName,
                            Sttp_Info.sttp_data.sttp_body.nFlag, Sttp_Info.sttp_data.sttp_body.ch_version);
                string strPtId = Sttp_Info.sttp_data.sttp_body.ch_pt_id;
                string strIedName = Sttp_Info.sttp_data.sttp_body.ch_HandlerName;
                string strRef = Sttp_Info.sttp_data.sttp_body.ch_version;
                string strErrMsg;
                int nChNoFind = -1;
                int ret = sttp2MMs40005(strIedName,strRef,nChNoFind,strErrMsg);
                if(ret !=0){
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 40005 ied[%s]参引[%s] 转换mms失败，回复rii[%d] 失败 的40006",m_strStnId.c_str(),
                            strIedName.c_str(),strRef.c_str(),Sttp_Info.sttp_data.sttp_head.uMsgRii);
                    Send40006Sttp(Sttp_Info.sttp_data.sttp_head.uMsgRii,0,strErrMsg,m_strStnId,strPtId,strIedName,strRef);
                }else{
                    CLockUp lockUp(&m_LockFor860Step);
                    map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(nChNoFind);
                    if(itNo!=m_map860IedList.end()){
                        if(itNo->second.nStep != RcvInitOk){
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 40005 ied[%s]参引[%s] 转换mms 成功，通道不通,step[%d]，不发，不缓存40005。直接回复40006",m_strStnId.c_str(),
                                    strIedName.c_str(),strRef.c_str(),itNo->second.nStep);
                            strErrMsg = "通道不通";
                            Send40006Sttp(Sttp_Info.sttp_data.sttp_head.uMsgRii,0,strErrMsg,m_strStnId,strPtId,strIedName,strRef);
                        }else{
                            RCV_40005_INFO s1;
                            s1.bIsDoing=true;
                            zero_sttp_full_data(s1.s40005Sttp.sttp_data);
                            s1.s40005Sttp = Sttp_Info;
                            s1.tRcvTime = time(0);
                            CLockUp lockUp1(&m_LockForCrc40005SttpCache);
                            m_mapCrc40005SttpCache.insert(make_pair(nChNoFind,s1));
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 40005 ied[%s]参引[%s] 转换mms 成功，缓存40005",m_strStnId.c_str(),strIedName.c_str(),strRef.c_str());
                        }
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 40005 ied[%s]参引[%s] 转换mms 成功，通道不存在，不发，不缓存40005。直接回复40006",m_strStnId.c_str(),strIedName.c_str(),strRef.c_str());
                        strErrMsg = "通道不存在";
                        Send40006Sttp(Sttp_Info.sttp_data.sttp_head.uMsgRii,0,strErrMsg,m_strStnId,strPtId,strIedName,strRef);
                    }
                }
            }
            break;
        default:
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] [%d]uMsgID not care",m_strStnId.c_str(),uMsgID);
            break;
        }
    }
    
    //整理检查前置来的信息，按通道分类填充，减少下面循环的次数
    {
        CLockUp lockUp(&m_LockForRcvHnTcMsgList);
        if(!m_RcvHnTcMsgList.empty()){
            int nMsg = m_RcvHnTcMsgList.size();
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] [%d]nMsg time",m_strStnId.c_str(),nMsg);
            for(int n=0;n<nMsg;n++){
                string strMsg = m_RcvHnTcMsgList.front();
                m_RcvHnTcMsgList.pop_front();
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] nMsg-n[%d] time",m_strStnId.c_str(),n);
            PrintBytes(strMsg);
                int nCanTc = IsHugeMMS(strMsg);//0-不是last要另存等全了判定转发，1-是Last可以透传,-1-判断失败
                if(nCanTc==1){
                    if(m_RcvHnTcHugeMsgList.empty()){//小mms，先判定ied然后入list
                        if(m_nUseMMS==1)//有数据库用mms解析库
                        {
                            Pack_TPKT vByte;
                            vByte.clear();
                            vByte.insert(vByte.end(),strMsg.begin(),strMsg.end());
                            list_TPKT list_tpkt;
                            list_tpkt.push_back(vByte);
                            bool bFail=false;
                            
                            MMS_Analyze sAnalyzeResult;
                            bool bok = m_pCMMSAnalyze->Analyze(list_tpkt,sAnalyzeResult);
                            
                            
                            if(bok){
                                //文件类的照旧
                                if( (sAnalyzeResult.pkt_type==ENUM_MMS_UNKNOW)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_GETNAMELIST)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_OPEN)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_READ)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_CLOSE)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_DIRECTORY)    )
                                {
                                    bool bFindIed=false;
                                    int nFindIedLenCh=-1;
                                    int ret = SearchIedFromMMs(strMsg,bFindIed,nFindIedLenCh);
                                    
                                    
                                    // 2024-01-29 lmy add 
                                   if(!isConnectChNo(nFindIedLenCh))
                                   {
                                       // 如果设备连着状态非连接成功，向前置发送错误通知，并不再透传
                                       SendError2Front(nFindIedLenCh,strMsg);
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d] 当初连接状态不为 RcvInitOk 不做透传 ",m_strStnId.c_str(),nFindIedLenCh);
                                       continue;
                                    }
                                    // end lmy
                                    
                                    if(bFindIed==false){
                                        bFail = true;
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS解析库文件类 前置来的短包len[%d] 所有ied没找到ied 不透传，ret[%d],剩余 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                        strMsg.length(),ret,m_RcvHnTcMsgList.size());
                                    }else{
                                        {
                                            CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                            map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(nFindIedLenCh);
                                            if(itCh!=m_mapRcvHnTcMsg.end()){
                                                itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS解析库文件类 通道号[%d]有自己的包 短包 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                    itCh->first,itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                            }else{
                                                bFail = true;//不太会
                                            }
                                        }
                                    }
                                    
                                }else{//非文件类的走解析库
                                    vector<DomainSpecific>::iterator itb=sAnalyzeResult.list_domian.begin();//假定报文里多个LD都是一个ied的，任一一个即可定位设备通道
                                    string strdomainID = (*itb).DomainID.c_str();
                                    string strItemID = (*itb).ItemID.c_str();
                                    map<string,string>::iterator itIed = m_mapLd2IedName.find(strdomainID);//<PL102LD0,PL102>
                                    if(itIed != m_mapLd2IedName.end()){
                                        map<string,int>::iterator itChNo = m_mapIedName2ChNo.find(itIed->second.c_str());
                                        if(itChNo != m_mapIedName2ChNo.end()){
                                            {
                                                  // 2024-01-29 lmy add 
                                                  if(!isConnectChNo(itChNo->second))
                                                  {
                                                      // 如果设备连着状态非连接成功，向前置发送错误通知，并不再透传
                                                      SendError2Front(itChNo->second,strMsg);
                                                      m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d] 当初连接状态不为 RcvInitOk 不做透传 ",m_strStnId.c_str(),itChNo->second);
                                                      continue;
                                                   }
                                                  // end lmy
                                   
                                   
                                                CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                                map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itChNo->second);
                                                if(itCh!=m_mapRcvHnTcMsg.end()){
                                                 /*   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS测试 domainID[%s]ItemID[%s] ",m_strStnId.c_str(),strdomainID.c_str(),strItemID.c_str());
                                                    if( (strcmp(strdomainID.c_str(),"PL1109CTRL")==0)&&(strcmp(strItemID.c_str(),"LLN0$dsDin")==0) ){
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS测试 招开关量不透传[%s][%s] ",m_strStnId.c_str(),strdomainID.c_str(),strItemID.c_str());
                                                    }else if( (strcmp(strdomainID.c_str(),"PL1109CTRL")==0)&&(strcmp(strItemID.c_str(),"LLN0$dsDin2")==0) ){
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS测试 招开关量不透传[%s][%s] ",m_strStnId.c_str(),strdomainID.c_str(),strItemID.c_str());
                                                    }else if( (strcmp(strdomainID.c_str(),"PL1109LD0")==0)&&(strcmp(strItemID.c_str(),"LLN0$dsRelayDin")==0) ){
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS测试 招开关量不透传[%s][%s] ",m_strStnId.c_str(),strdomainID.c_str(),strItemID.c_str());
                                                    }else if( (strcmp(strdomainID.c_str(),"PL1109PROT")==0)&&(strcmp(strItemID.c_str(),"LLN0$dsRelayDin2")==0) ){
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS测试 招开关量不透传[%s][%s] ",m_strStnId.c_str(),strdomainID.c_str(),strItemID.c_str());
                                                    }else{
                                                        itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包 短包,invokeID[%d]iedname[%s], 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                            itCh->first,sAnalyzeResult.invokeID,strdomainID.c_str(),itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                                    }*/
                                                    itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包 短包,invokeID[%d]iedname[%s], 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                        itCh->first,sAnalyzeResult.invokeID,strdomainID.c_str(),itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                                }else{
                                                    bFail = true;//不太会
                                                }
                                            }
                                        }else{
                                            bFail = true;
                                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 报文Analyze结果 domainID[%s]的iedname[%s] 找不到 通道号,invokeID[%d]iedname[%s]",m_strStnId.c_str(),
                                                    strdomainID.c_str(),itIed->second.c_str(),sAnalyzeResult.invokeID,strdomainID.c_str());
                                        }
                                    }else{
                                        bFail = true;
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 报文Analyze结果 domainID[%s]找不到 iedname,invokeID[%d]",m_strStnId.c_str(),
                                                    strdomainID.c_str(),sAnalyzeResult.invokeID);
                                    }
                                }
                            }else{
                                bFail = true;
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]小包MMS 解析失败,长度tpktMsg[%d]invokeID[%d]",m_strStnId.c_str(),
                                                    strMsg.length(),sAnalyzeResult.invokeID);
                            }
                            
                            if(bFail){
                                int r = SendError2Front(0,strMsg);
                                if(r != 0){
                                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 短包识别失败，回复前置err包失败",m_strStnId.c_str());
                                }
                            }
                            
                            
                        }
                        else//没数据库得不到PL102LD0 的 domainId的关系，走老模式
                        {
                            bool bFindIed=false;
                            int nFindIedLenCh=-1;
                            int ret = SearchIedFromMMs(strMsg,bFindIed,nFindIedLenCh);

                            if(bFindIed==false){
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 前置来的短包len[%d] 所有ied没找到ied 不透传，ret[%d],剩余 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                strMsg.length(),ret,m_RcvHnTcMsgList.size());
                            }
                            else{
                                {
                                    CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                    map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(nFindIedLenCh);
                                    if(itCh!=m_mapRcvHnTcMsg.end()){
                                        itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包 短包 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                            itCh->first,itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                    }else{
                                        //不太会
                                    }
                                }
                            }
                        }
                        
                    }else{//前面有大mms等到last,合并判定ied，然后一轮发出
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查是否有自己的包 huge收到last完整的cotp,最后一条msg大小[%d]目前前面缓存tpkt[%d]条，前置待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                    strMsg.size(),m_RcvHnTcHugeMsgList.size(),m_RcvHnTcMsgList.size());
                        //合并多个tpkt，用于检索ied
                        vector<BYTE> vAllMsg;
                        for(list<string>::iterator it=m_RcvHnTcHugeMsgList.begin();it!=m_RcvHnTcHugeMsgList.end();it++){
                            vAllMsg.insert(vAllMsg.end(),it->begin(),it->end());
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查是否有自己的包 合并Huge包，插入长度[%d],现在长度[%d]",m_strStnId.c_str(),
                                it->size(),vAllMsg.size());
                        }
                        vAllMsg.insert(vAllMsg.end(),strMsg.begin(),strMsg.end());
                        string strAllMsg;
                        strAllMsg.assign(vAllMsg.begin(),vAllMsg.end());
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查是否有自己的包 合并Huge包最后一帧，插入长度[%d],现在长度[%d],str长度[%d]",m_strStnId.c_str(),
                                strMsg.size(),vAllMsg.size(),strAllMsg.size());
                        
                        if(m_nUseMMS==1)//有数据库用mms解析库
                        {
                            bool bFail=false;
                            list_TPKT list_tpkt;
                            for(list<string>::iterator it=m_RcvHnTcHugeMsgList.begin();it!=m_RcvHnTcHugeMsgList.end();it++){
                                Pack_TPKT vByte;
                                vByte.clear();
                                vByte.insert(vByte.end(),it->begin(),it->end());
                                list_tpkt.push_back(vByte);
                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查是否有自己的包 合并Huge包 m_RcvHnTcHugeMsgList push_back ",m_strStnId.c_str());
                                PrintBytes(*it);
                            }
                            Pack_TPKT vByte;
                            vByte.clear();
                            vByte.insert(vByte.end(),strMsg.begin(),strMsg.end());
                            list_tpkt.push_back(vByte);
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查是否有自己的包 合并Huge包 m_RcvHnTcHugeMsgList push_back last",m_strStnId.c_str());
                            PrintBytes(strMsg);
                            
                            MMS_Analyze sAnalyzeResult;
                            bool bok = m_pCMMSAnalyze->Analyze(list_tpkt,sAnalyzeResult);
                            if(bok){
                                //文件类的照旧
                                if( (sAnalyzeResult.pkt_type==ENUM_MMS_UNKNOW)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_GETNAMELIST)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_OPEN)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_READ)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_CLOSE)
                                        ||(sAnalyzeResult.pkt_type==ENUM_MMS_FILE_DIRECTORY)    )
                                {
                                    bool bFindIed=false;
                                    int nFindIedLenCh=-1;
                                    int ret = SearchIedFromMMs(strMsg,bFindIed,nFindIedLenCh);
                                    
                                    if(bFindIed==false){
                                        bFail = true;
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS解析库文件类 huge大 包 所有ied没找到ied，ret[%d], 剩余 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                        ret,m_RcvHnTcMsgList.size());
                                    }else{
                                        CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                        map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(nFindIedLenCh);
                                        if(itCh!=m_mapRcvHnTcMsg.end()){
                                            int nSize=m_RcvHnTcHugeMsgList.size();
                                            for(int i=0;i<nSize;i++){
                                                string strMsgHug = m_RcvHnTcHugeMsgList.front();
                                                m_RcvHnTcHugeMsgList.pop_front();
                                                itCh->second.RcvHnTcMsgList.push_back(strMsgHug);
                                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS解析库文件类 huge大包 通道号[%d]有自己的包 huge大包缓存，一条未完整的cotp大小[%d]剩余 huge缓存[%d]条，该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                    itCh->first,strMsgHug.size(),m_RcvHnTcHugeMsgList.size(),itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                            }
                                            m_RcvHnTcHugeMsgList.clear();

                                            itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS解析库文件类 huge大包 通道号[%d]有自己的包，huge大包最后一条, 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                itCh->first,itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                        }else{
                                            bFail = true;//不太会
                                        }
                                    }
                                }else{//非文件类的走解析库
                                    vector<DomainSpecific>::iterator itb=sAnalyzeResult.list_domian.begin();
                                    string strdomainID = (*itb).DomainID.c_str();
                                    map<string,string>::iterator itIed = m_mapLd2IedName.find(strdomainID);//<PL102LD0,PL102>
                                    if(itIed != m_mapLd2IedName.end()){
                                        map<string,int>::iterator itChNo = m_mapIedName2ChNo.find(itIed->second.c_str());
                                        if(itChNo != m_mapIedName2ChNo.end()){
                                            {
                                                CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                                map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itChNo->second);
                                                if(itCh!=m_mapRcvHnTcMsg.end()){
                                                    int nSize = m_RcvHnTcHugeMsgList.size();
                                                    for(int i=0;i<nSize;i++){
                                                        string strMsgHug = m_RcvHnTcHugeMsgList.front();
                                                        m_RcvHnTcHugeMsgList.pop_front();
                                                        itCh->second.RcvHnTcMsgList.push_back(strMsgHug);
                                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包 huge大包缓存,invokeID[%d]，一条未完整的cotp大小[%d]剩余 huge缓存[%d]条，该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                            itCh->first,sAnalyzeResult.invokeID,strMsgHug.size(),m_RcvHnTcHugeMsgList.size(),itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                                    }
                                                    m_RcvHnTcHugeMsgList.clear();

                                                    itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包，huge大包最后一条,invokeID[%d], 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                        itCh->first,sAnalyzeResult.invokeID,itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                                }else{
                                                    bFail = true;//不太会
                                                }
                                            }
                                        }else{
                                            bFail = true;
                                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]huge大包 MMS 报文Analyze结果 domainID[%s]的iedname[%s] 找不到 通道号,invokeID[%d]",m_strStnId.c_str(),
                                                    strdomainID.c_str(),itIed->second.c_str(),sAnalyzeResult.invokeID);
                                        }
                                    }else{
                                        bFail = true;
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]huge大包 MMS 报文Analyze结果 domainID[%s]找不到 iedname,invokeID[%d] ",m_strStnId.c_str(),
                                                    strdomainID.c_str(),sAnalyzeResult.invokeID);
                                    }
                                }
                            }else{
                                bFail = true;
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]大包MMS 解析失败,长度AllTpktMsg[%d]invokeID[%d]",m_strStnId.c_str(),
                                                    strAllMsg.length(),sAnalyzeResult.invokeID);
                            }
                            
                            if(bFail){
                                int r = SendError2Front(0,strAllMsg);
                                if(r != 0){
                                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 长包识别失败，回复前置err包失败",m_strStnId.c_str());
                                }
                            }
                            
                        }
                        else//没数据库得不到PL102LD0 的 domainId的关系，走老模式
                        {
                            // 这里面处理前面接收到的所有连包数据和最后一包
                            bool bFindIed=false;
                            int nFindIedLenCh=-1;
                            // star 2023/11/30 lmy modify   不在从最后一包数据获取 ied_name 改从第一包获取 
                           // int ret = SearchIedFromMMs(strMsg,bFindIed,nFindIedLenCh);    // 此处屏蔽原来从最后一包包获取
                            
                             string strFirstMsg = m_RcvHnTcHugeMsgList.front();
                             int ret = SearchIedFromMMs(strFirstMsg,bFindIed,nFindIedLenCh);
                            
                            // end 2023/11/30 lmy
                                    
                            if(bFindIed==false){
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS huge大 包 所有ied没找到ied，ret[%d],剩余 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                                ret,m_RcvHnTcMsgList.size());
                            }else{
                                CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                                map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(nFindIedLenCh);
                                if(itCh!=m_mapRcvHnTcMsg.end()){
                                    int nSize=m_RcvHnTcHugeMsgList.size();
                                    for(int i=0;i<nSize;i++){
                                        string strMsgHug = m_RcvHnTcHugeMsgList.front();
                                        m_RcvHnTcHugeMsgList.pop_front();
                                        itCh->second.RcvHnTcMsgList.push_back(strMsgHug);
                                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包 huge大包缓存，一条未完整的cotp大小[%d]剩余 huge缓存[%d]条，该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                            itCh->first,strMsgHug.size(),m_RcvHnTcHugeMsgList.size(),itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                    }
                                    m_RcvHnTcHugeMsgList.clear();

                                    itCh->second.RcvHnTcMsgList.push_back(strMsg);
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 通道号[%d]有自己的包，huge大包最后一条, 剩余 该设备待透传msg[%d]条 站待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                        itCh->first,itCh->second.RcvHnTcMsgList.size(),m_RcvHnTcMsgList.size());
                                }else{
                                    //不太会
                                }
                            }
                        }
                        
                    }
                }else if(nCanTc==0){
                    m_RcvHnTcHugeMsgList.push_back(strMsg);//前置来的缓存不分设备，应该安全
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 检查时发现huge包，另外缓存一条未完整的cotp大小[%d]目前缓存[%d]条，待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                            strMsg.size(),m_RcvHnTcHugeMsgList.size(),m_RcvHnTcMsgList.size());
                }else{
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 包异常 大小[%d]目前缓存[%d]条，待向设备透传 msg[%d]条 ",m_strStnId.c_str(),
                                strMsg.size(),m_RcvHnTcHugeMsgList.size(),m_RcvHnTcMsgList.size());
                    if(m_nUseMMS==1)//有数据库用mms解析库
                    {
                        int r = SendError2Front(0,strMsg);//-1
                        if(r != 0){
                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 包完整性 识别失败，回复前置err包失败",m_strStnId.c_str());
                        }
                    }
                }
            }
        }
    }
    
    //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 状态机管理 time m_bCanProxy[%d]",m_strStnId.c_str(),m_bCanProxy);
    //printf("CXJPro103ClientWay::StnCfgInitLoop() m_bCanProxy[%d]m_map860IedList[%d]\n",m_bCanProxy,m_map860IedList.size());
    
//m_bCanProxy=true;
    if(m_bCanProxy){
//printf("--------bCanProxy m_map860IedList[%d]",m_map860IedList.size());
        //管理建连
        CLockUp lockUp(&m_LockFor860Step);
        for( map<int,STEP_INFO>::iterator itNo=m_map860IedList.begin();itNo!=m_map860IedList.end();itNo++ ){
//printf("--------bCanProxy no[%d] step[%d]now[[%d]]tStepNextTime[%d]",itNo->first,itNo->second.nStep,time(NULL),itNo->second.tStepNextTime);
//            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()] 状态机管理 time 通道号[%d] step[%d]now[[%d]]tStepNextTime[%d]",m_strStnId.c_str(),
//                        itNo->first,itNo->second.nStep,time(NULL),itNo->second.tStepNextTime);
            //遵循周期间隔倍增管理，减少不通的设备不停close的资源消耗
            time_t tNow =  time(NULL);
            if( tNow >= itNo->second.tStepNextTime){
                if(itNo->second.nStep == LinkUnknow){
                    //不能在这里，在这里每wile1都发，量太大，只能周期发；SendPtChNo20002(itNo->first,2);
                    SendAsdu200CloseConn(itNo->first);
                    itNo->second.nStep = LinkWtClose;
                    if(itNo->second.tStepCur!=0){//重试间隔每次延长，避免不停重试，上限600s
                        time_t tNextPlus = 0;
                        if(itNo->second.nFailCount > 5){
                            tNextPlus = (itNo->second.nFailCount*2)>120 ? 120 : (itNo->second.nFailCount*2);
                        }
                        itNo->second.tStepNextTime = tNow + tNextPlus;
                    }
                    itNo->second.tStepCur = tNow;
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]step LinkUnknow通道号[%d]当前为unkonow，尝试关闭连接，tStepCur[%d]tStepNextTime[%d]nFailCount[%d]",m_strStnId.c_str()
                                            ,itNo->first,itNo->second.tStepCur,itNo->second.tStepNextTime,itNo->second.nFailCount);
                    
                }else 
                if(itNo->second.nStep == LinkClosed){
                    //关闭连接后清除残留的upto102的缓存，能清的是完整的未发的包  或者  未完整的未发的包
                    {
                        CLockUp lockUp(&m_LockForSendHnTcMsgList);
                        map<int,CHNO_TC_MSG >::iterator it=m_mapNoTcMsg.find(itNo->first);
                        if(it!=m_mapNoTcMsg.end()){
                           it->second.vFullMsg.clear();
                           it->second.listHugeMsg.clear();
                        }
                    }
                    //关闭连接后清除残留的102downto ied的缓存，能清的是完整的未发的包  或者  未完整的未发的包
                    {
                        CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                        map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itNo->first);
                        if(itCh!=m_mapRcvHnTcMsg.end()){
                            itCh->second.RcvHnTcMsgList.clear();
                        }else{
                            //不太会
                        }
                    }
                    //发40004？
                    Send40004Sttp(itNo->first,0);
                    //不能在这里，在这里每wile1都发，量太大，只能周期发；SendPtChNo20002(itNo->first,1);
                    //next step
                    SendAsdu200Conn(itNo->first);
                    itNo->second.nStep = LinkWtConn;
                    if(itNo->second.tStepCur!=0){
                        time_t tNextPlus = 0;
                        if(itNo->second.nFailCount > 5){
                            tNextPlus = (itNo->second.nFailCount*2)>120 ? 120 : (itNo->second.nFailCount*2);
                        }
                        itNo->second.tStepNextTime = tNow + tNextPlus;
                    }
                    itNo->second.tStepCur = tNow;
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]step LinkClosed通道号[%d]关闭了链接，尝试建立连接，tStepCur[%d]tStepNextTime[%d]nFailCount[%d]",m_strStnId.c_str()
                                            ,itNo->first,itNo->second.tStepCur,itNo->second.tStepNextTime,itNo->second.nFailCount);
                }else 
                if(itNo->second.nStep == LinkConned){
                    SendAsdu200CR(itNo->first);
                    //考虑连上后再招
                    itNo->second.nStep = WtReCC;
                    itNo->second.tStepNextTime = tNow;
                    itNo->second.tStepCur = tNow;
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]step LinkConned通道号[%d]建立了链接，尝试CR，tStepCur[%d]tStepNextTime[%d]",m_strStnId.c_str(),itNo->first,itNo->second.tStepCur,itNo->second.tStepNextTime);
                }else 
                if(itNo->second.nStep == RcvCC){
                    SendAsdu200Init(itNo->first);
                    itNo->second.nStep = WtReInit;
                    itNo->second.tStepNextTime = tNow;
                    itNo->second.tStepCur = tNow;
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]step RcvCC通道号[%d]收到CC，尝试发送INIT，tStepCur[%d]tStepNextTime[%d]",m_strStnId.c_str(),itNo->first,itNo->second.tStepCur,itNo->second.tStepNextTime);
                }else 
                if(itNo->second.nStep == RcvInitOk){
                    itNo->second.tStepNextTime = tNow;
                    itNo->second.tStepCur = tNow;
                    
                    CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                    map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itNo->first);
                    if(itCh!=m_mapRcvHnTcMsg.end()){
                        if(!itCh->second.RcvHnTcMsgList.empty()){
                            //发一条，不全发，留机会给每个设备都能发一波；
                            string str = itCh->second.RcvHnTcMsgList.front();
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 状态机管理 发送通道[%d],该通道有[%d]条待发送 time",m_strStnId.c_str(),itNo->first,itCh->second.RcvHnTcMsgList.size());
                            PrintBytes(str); 
                            SendAsdu200Tc(str,itNo->first);
                            itCh->second.RcvHnTcMsgList.pop_front();
                        }
                    }else{
                        //不太会
                    }
                }else{
                    if( (itNo->second.nStep != LinkWtClose)&&(itNo->second.nStep != LinkWtConn)&&(itNo->second.nStep != WtReCC)&&(itNo->second.nStep != WtReInit) ){
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s]通道号[%d]step 状态不正确 [%d]",m_strStnId.c_str(),itNo->first,itNo->second.nStep);
                    }
                }

                if(itNo->second.nStep != RcvInitOk){
                    //超时退回状态，触发重发,不能放前面，问题也不大，会一上来就超时一次
                    if( (tNow - itNo->second.tStepCur)>60 ){
                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop][%d]超时[%d],now step[%d] ,try backto ",m_strStnId.c_str(),tNow,itNo->second.tStepCur,itNo->second.nStep);
                        if(itNo->second.nStep == LinkWtClose){
                            itNo->second.nStep = LinkUnknow;
                            itNo->second.nFailCount = (itNo->second.nFailCount>65535) ? 65535 : (itNo->second.nFailCount+1);
                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop][%d]超时[%d],step back to LinkUnknow[%d]nFailCount++[%d]",m_strStnId.c_str(),tNow,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
                        }else 
                        if(itNo->second.nStep == LinkWtConn){
                            itNo->second.nStep = LinkClosed;
                            itNo->second.nFailCount = (itNo->second.nFailCount>65535) ? 65535 : (itNo->second.nFailCount+1);
                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop][%d]超时[%d],step back to LinkClosed[%d]nFailCount++[%d]",m_strStnId.c_str(),tNow,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
                        }else 
                        if(itNo->second.nStep == WtReCC){
                            itNo->second.nStep = LinkUnknow;
                            itNo->second.nFailCount = (itNo->second.nFailCount>65535) ? 65535 : (itNo->second.nFailCount+1);
                            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::StnCfgInitLoop][%d]超时[%d],step back to LinkConned[%d]nFailCount++[%d]",m_strStnId.c_str(),tNow,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
                        }else 
                        if(itNo->second.nStep == WtReInit){
                            itNo->second.nStep = RcvCC;
                            itNo->second.nFailCount = (itNo->second.nFailCount>65535) ? 65535 : (itNo->second.nFailCount+1);
                            m_rLogFile.FormatAdd(CLogFile::error,"[%S][CXJPro103ClientWay::StnCfgInitLoop][%d]超时[%d],step back to RcvCR[%d]nFailCount++[%d]",m_strStnId.c_str(),tNow,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
                        }
                    }
                    
                    //如果该通道有报文，回复失败，不然可能会导致前置卡住
                    CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
                    map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itNo->first);
                    if(itCh!=m_mapRcvHnTcMsg.end()){
                        if(!itCh->second.RcvHnTcMsgList.empty()){
                            string str = itCh->second.RcvHnTcMsgList.front();
                            PrintBytes(str); 
                            int r = SendError2Front(itNo->first,str);
                            if(r == 0){
                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 状态机管理未到init回复失败包，回复成功， 发送通道[%d],该通道有[%d]条待发送 time",m_strStnId.c_str(),
                                    itNo->first,itCh->second.RcvHnTcMsgList.size());
                                itCh->second.RcvHnTcMsgList.pop_front();
                            }else{
                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::StnCfgInitLoop()]MMS 状态机管理未到init回复失败包，回复失败， 发送通道[%d],该通道有[%d]条待发送 time",m_strStnId.c_str(),
                                    itNo->first,itCh->second.RcvHnTcMsgList.size());
                            }
                        }
                    }else{
                        //不太会
                    }
                }
            }
        }
    
    }
    
    return 0;
}



int CXJPro103ClientWay::ResetIedStep()
{
    time_t tNow=time(NULL);
    CLockUp lockUp(&m_LockFor860Step);
    for( map<int,STEP_INFO>::iterator itNo=m_map860IedList.begin();itNo!=m_map860IedList.end();itNo++ ){
        itNo->second.nStep = LinkUnknow;
        itNo->second.tStepCur = tNow;
        itNo->second.tStepNextTime = tNow;
        itNo->second.nFailCount = 0;
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::ResetIedStep][%d]front断开，通道号[%d],step back to LinkUnknow[%d] nFailCount[%d]",m_strStnId.c_str()
                            ,tNow,itNo->first,itNo->second.nStep,itNo->second.nFailCount);

    }
    {
        CLockUp lockUp(&m_LockForRcvHnTcMsgList);
        m_RcvHnTcMsgList.clear();
        m_RcvHnTcHugeMsgList.clear();
    }
    return 0;
}
int CXJPro103ClientWay::ResetOneIedStep(string strPtId,u_short uSttpRii)
{
    time_t tNow=time(NULL);
    int nChNo=-1;
    
    map<string, int>::iterator it = m_mapDevIdCh2.find(strPtId);
    if(it!=m_mapDevIdCh2.end()){
        nChNo = it->second;
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::ResetOneIedStep][%d]收到[%s]的40003-rii[%d]，列表[总计%d条]未找到该设备的通道号，放弃复位该通道",m_strStnId.c_str(),
                tNow,strPtId.c_str(),uSttpRii, m_mapDevIdCh2.size());
        return -1;
    }
    //复位该通道状态机
    CLockUp lockUp(&m_LockFor860Step);
    map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(nChNo);
    if(itNo!=m_map860IedList.end()){
        {
            //记录rii，关闭后回40004，几乎不会失败，失败在外面回40004
            CLockUp lockUp(&m_LockForSend40003Sttp);
            map<int,set<u_short> >::iterator it=m_mapSttpCloseChNoRii.find(nChNo);
            if(it!=m_mapSttpCloseChNoRii.end()){
                it->second.insert(uSttpRii);
            }else{
                //不会
            }
        }
        itNo->second.nStep = LinkUnknow;
        itNo->second.tStepCur = tNow;
        itNo->second.tStepNextTime = tNow;
        itNo->second.nFailCount = 0;
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::ResetOneIedStep][%d]收到[%s]的40003-rii[%d]，通道号[%d],step [%d]back to LinkUnknow[%d] nFailCount[%d]",m_strStnId.c_str(),
                tNow,strPtId.c_str(),uSttpRii,nChNo,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
        SendPtChNo20002(itNo->first,2);//放这里，因为step往前进数量少，不像状态机里数量多。
        
    }else{
        int ret = SecDevFlowMoudle::getInstanse()->setStep(m_strStnId, nChNo, 1);
        if (-1 == ret)
        {
            m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::ResetOneIedStep][%d]收到[%s]的40003-rii[%d]，未找到该设备的通道号[%d]的状态位，放弃复位该通道", m_strStnId.c_str(),
                tNow, strPtId.c_str(), uSttpRii, nChNo);
            return -2;
        }
        else
        {
            m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::ResetOneIedStep][%d]收到[%s]的40003-rii[%d]，通道号[%d], 重置", m_strStnId.c_str(),
                tNow, strPtId.c_str(), uSttpRii, nChNo);
        }
    }
    
//    设备级缓存清理,不清理了，因为前置可能同时发出关闭通道和新信息
//    CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
//    map<int,RCV_TC_INFO>::iterator itCh = m_mapRcvHnTcMsg.find(nChNo);
//    if(itCh!=m_mapRcvHnTcMsg.end()){
//        itCh->second.RcvHnTcMsgList.clear();
//    }
    //站级缓存不清 m_RcvHnTcMsgList.clear();里面有残留也只能发了。基本没有残留
    //站级缓存不清 m_RcvHnTcHugeMsgList.clear();里面有残留也只能发了。基本没有残留
    return 0;
}
int CXJPro103ClientWay::ResetOneIedStep(int nChNo)
{
    time_t tNow=time(NULL);
    //复位该通道状态机
    CLockUp lockUp(&m_LockFor860Step);
    map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(nChNo);
    if(itNo!=m_map860IedList.end()){
        itNo->second.nStep = LinkUnknow;
        itNo->second.tStepCur = tNow;
        itNo->second.tStepNextTime = tNow;
        itNo->second.nFailCount = 0;
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::ResetOneIedStep]复位状态机 通道号[%d],stepcur[%d]back to LinkUnknow[%d] nFailCount[%d]",m_strStnId.c_str(),
                nChNo,itNo->second.tStepCur,itNo->second.nStep,itNo->second.nFailCount);
        SendPtChNo20002(itNo->first,2);//放这里，因为step往前进数量少，不像状态机里数量多。
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::ResetOneIedStep]复位状态机 未找到该设备的通道号[%d]的状态位，放弃复位该通道",m_strStnId.c_str(),nChNo);
        return -2;
    }
    return 0;
}



int CXJPro103ClientWay::Send40004Sttp(int ChNo,int nOK)
{
    CLockUp lockUp(&m_LockForSend40003Sttp);
    map<int,set<u_short> >::iterator it=m_mapSttpCloseChNoRii.find(ChNo);
    if(it!=m_mapSttpCloseChNoRii.end()){
        if(it->second.empty()){
            return 0;
        }else{
            map<int,string>::iterator itPtId=m_mapCh2DevId.find(ChNo);
            if(itPtId!=m_mapCh2DevId.end()){
                for(set<u_short >::iterator itRii=it->second.begin();itRii!=it->second.end();itRii++){
                    STTP_FULL_DATA sttp_data40004;  
                    zero_sttp_full_data(sttp_data40004);
                    sttp_data40004.sttp_head.uMsgID = 40004;
                    sttp_data40004.sttp_head.uMsgType = 'I';
                    sttp_data40004.sttp_head.uMsgRii = *itRii;

                    snprintf(sttp_data40004.sttp_body.ch_station_id,sizeof(sttp_data40004.sttp_body.ch_station_id),"%s",m_strStnId.c_str());
                    snprintf(sttp_data40004.sttp_body.ch_pt_id,sizeof(sttp_data40004.sttp_body.ch_pt_id),"%s",itPtId->second.c_str());
                    sttp_data40004.sttp_body.nFlag = 1;//0-配置生效  1-关闭通道
                    sttp_data40004.sttp_body.nStatus = nOK;//1-失败 0-成功

                    BUS_RECV_STTPFULLDATA_INFO Sttp_Info40004; //40004
                    Sttp_Info40004.sttp_data = sttp_data40004;
                    Sttp_Info40004.t_time = time(NULL);
                    {
                        CLockUp lockUp(&m_LockForSendBusList);
                        m_SendBusList.push_back(Sttp_Info40004);
                    }
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Send40004Sttp]step  通道[%d] 关闭成功 4003的rii[%d]回复40004",m_strStnId.c_str(),ChNo,*itRii);
                }
            }else{
                //打印失败
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Send40004Sttp]step  通道[%d]没找到ptid 40004失败",m_strStnId.c_str(),ChNo);
                return -1;
            }
        }
    }
    return 0;
}
int CXJPro103ClientWay::Send40004Sttp(string strPtId,int nOK,u_short uSttpRii)
{
    STTP_FULL_DATA sttp_data40004;  
    zero_sttp_full_data(sttp_data40004);
    sttp_data40004.sttp_head.uMsgID = 40004;
    sttp_data40004.sttp_head.uMsgType = 'I';
    sttp_data40004.sttp_head.uMsgRii = uSttpRii;

    snprintf(sttp_data40004.sttp_body.ch_station_id,sizeof(sttp_data40004.sttp_body.ch_station_id),"%s",m_strStnId.c_str());
    snprintf(sttp_data40004.sttp_body.ch_pt_id,sizeof(sttp_data40004.sttp_body.ch_pt_id),"%s",strPtId.c_str());
    sttp_data40004.sttp_body.nFlag = 1;//0-配置生效  1-关闭通道
    sttp_data40004.sttp_body.nStatus = nOK;//1-失败 0-成功

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info40004; //40004通信状态
    Sttp_Info40004.sttp_data = sttp_data40004;
    Sttp_Info40004.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info40004);
    }
                
    return 0;
}
int CXJPro103ClientWay::Send40006Sttp(int nRii,int nOK,string strMsg,string strStnId,string strPtId,string strIedName,string strRef)
{
    STTP_FULL_DATA sttp_data40006;  
    zero_sttp_full_data(sttp_data40006);
    sttp_data40006.sttp_head.uMsgID = 40006;
    sttp_data40006.sttp_head.uMsgType = 'I';
    sttp_data40006.sttp_head.uMsgRii = nRii;

    snprintf(sttp_data40006.sttp_body.ch_station_id,sizeof(sttp_data40006.sttp_body.ch_station_id),"%s",strStnId.c_str());
    snprintf(sttp_data40006.sttp_body.ch_pt_id,sizeof(sttp_data40006.sttp_body.ch_pt_id),"%s",strPtId.c_str());
    snprintf(sttp_data40006.sttp_body.ch_HandlerName,sizeof(sttp_data40006.sttp_body.ch_HandlerName),"%s",strIedName.c_str());
    snprintf(sttp_data40006.sttp_body.ch_version,sizeof(sttp_data40006.sttp_body.ch_version),"%s",strRef.c_str());
    
    sttp_data40006.sttp_body.nFlag = 1;//1：装置软件版本
    sttp_data40006.sttp_body.nStatus = nOK;//0-失败；1-成功
    sttp_data40006.sttp_body.strMessage = strMsg;//结果值

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info40006; //40006crc回复
    Sttp_Info40006.sttp_data = sttp_data40006;
    Sttp_Info40006.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info40006);
    }
                
    return 0;
}
int CXJPro103ClientWay::Send40006Result(int nChNo,int nOK,string strMsg)
{
    CLockUp lockUp1(&m_LockForCrc40005SttpCache);
    map<int,RCV_40005_INFO >::iterator it = m_mapCrc40005SttpCache.find(nChNo);
    if(it != m_mapCrc40005SttpCache.end()){
        time_t tRcv = it->second.s40005Sttp.t_time;
        int nRii = it->second.s40005Sttp.sttp_data.sttp_head.uMsgRii;
        string strPtId = it->second.s40005Sttp.sttp_data.sttp_body.ch_pt_id;
        string strIedName = it->second.s40005Sttp.sttp_data.sttp_body.ch_HandlerName;
        string strRef = it->second.s40005Sttp.sttp_data.sttp_body.ch_version;
        Send40006Sttp(nRii,nOK,strMsg,m_strStnId,strPtId,strIedName,strRef);
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Send40006Result()] 通道[%d] 找到40005?捍?,rii[%d],时间[%d],删除缓存的40005sttp",m_strStnId.c_str(),nChNo,nRii,tRcv); 
        m_mapCrc40005SttpCache.erase(it);
        return 0;
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Send40006Result()] 通道[%d] 没找到40005缓存",m_strStnId.c_str(),nChNo);
        return -1;
    }
    
    return 0;
}
int CXJPro103ClientWay::SendError2Front(int nChNo,string &strOrgTpkt)
{
    if(m_nUseMMS!=1){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d],解析库开关未开",m_strStnId.c_str(),nChNo);
        return -1;
    }
    
    Pack_TPKT vByteOneTpkt;
    list_TPKT listTokt;
    vByteOneTpkt.assign(strOrgTpkt.begin(),strOrgTpkt.end());
    listTokt.push_back(vByteOneTpkt);
    unsigned int nInvokeID = -1;
    bool bMMsOk = m_pCMMSAnalyze->Analyze_InvokeID(listTokt,nInvokeID);
    if(bMMsOk){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d],解析内容nInvokeID[%d]",m_strStnId.c_str(),nChNo,nInvokeID);
        string strErrTpkt;
        m_pCMMSAnalyze->Pack_ServiceEorr(nInvokeID,strErrTpkt);
        if(!strErrTpkt.empty()){
            CLockUp lockUp(&m_LockForSendHnTcMsgList);
            map<int,CHNO_TC_MSG >::iterator itN = m_mapNoTcMsg.find(nChNo);
            if(itN != m_mapNoTcMsg.end()){
                itN->second.vFullMsg.insert(itN->second.vFullMsg.end(),strErrTpkt.begin(),strErrTpkt.end());
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d] 发送失败TPKT数据包 len[%d]",m_strStnId.c_str(),nChNo,strOrgTpkt.length());
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d] 没找到通道 不发送 不删除队列中该条",m_strStnId.c_str(),nChNo);
                return -2;
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d] 解析库生成error包失败",m_strStnId.c_str(),nChNo);
            return -3;
        }
    }else{
        //虽然不好，先这样处理，后面看要不要删除itCh->second.RcvHnTcMsgList.pop_front();
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CTaskMngr::SendError2Front()]MMS 回复失败报文 通道[%d],解析内容nInvokeID失败,不删除缓存，等待能连上吧",m_strStnId.c_str(),nChNo);
        return -4;
    }
    
    return 0;
}

//向子站
int CXJPro103ClientWay::CallAsdu200Crc()
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CallAsdu200Crc] CRC 召唤",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = 0;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketCallVersion;//包类型
    sttp_data.sttp_body.strMessage.clear();//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
int CXJPro103ClientWay::CallAsdu200GrpItemOneGrpItems(uint8 nGroup)
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CallAsdu200GrpItemOneGrpItems]nGroup[%d]",m_strStnId.c_str(),nGroup);
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = 0;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = 2;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketCallItem;//包类型
    
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),1,nGroup);
    vBytes.insert(vBytes.end(),1,255);
    
    string str1(vBytes.begin(),vBytes.end());
    sttp_data.sttp_body.strMessage = str1;//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
int CXJPro103ClientWay::CallAsdu200GrpItem(uint8 nGroup,set<string> setItems)
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CallAsdu200GrpItem] nGroup[%d]setItems",m_strStnId.c_str(),nGroup);
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = 0;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PTypeUnknow;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nCmdSource = ParketCallItem;//包类型
    
    unsigned int uNumItem = setItems.size();
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),1,nGroup);
    vBytes.insert(vBytes.end(),1,uNumItem);
    
    for(set<string>::iterator it=setItems.begin();it!=setItems.end();it++){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CallAsdu200GrpItem] setItems[%d]",m_strStnId.c_str(),it->c_str());
        uint16 uVal = atoi(it->c_str());
        uint8 uH,uL;
        uL = (uint8)(uVal & 0xff);
        uH = (uint8)(uVal>>8 & 0xff);
        //printf("uVal[%d] = uL[%d]0x[%02x] + uH[%d]0x[%02x]",uVal,uL,uL,uH,uH);
        vBytes.insert(vBytes.end(),1,uL);
        vBytes.insert(vBytes.end(),1,uH);
    }
//   m_rLogFile.FormatAdd(CLogFile::trace,"vBytesSize[%d]end[%d]",vBytes.size(),*vBytes.end());
//   m_rLogFile.FormatAdd(CLogFile::trace,"BYTEs");
//    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
//       m_rLogFile.FormatAdd(CLogFile::trace,"0x[%02x]",*it);
//    }
//   m_rLogFile.FormatAdd(CLogFile::trace,"");
    string str1(vBytes.begin(),vBytes.end());
//    m_rLogFile.FormatAdd(CLogFile::trace,"str1[%s]",str1.c_str());
//    unsigned char buffer[str1.length()];
//    memcpy(buffer, str1.data(), str1.length());
//   m_rLogFile.FormatAdd(CLogFile::trace,"str1");
//    for(int i=0;i<str1.length();i++){
//       m_rLogFile.FormatAdd(CLogFile::trace,"0x[%02x]",buffer[i]);
//    }
//   m_rLogFile.FormatAdd(CLogFile::trace,"");
    
    
    sttp_data.sttp_body.strMessage = str1;//数据包
    
    sttp_data.sttp_body.nStatus = vBytes.size();//数据包字节数
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
int CXJPro103ClientWay::CallAsdu200CfgEnable()
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = 0;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PTypeUnknow;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketCfgEnable;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
int CXJPro103ClientWay::CallCfgInfoFile()
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 210;
    sttp_data.sttp_head.uMsgType = 'I';
    //sttp_data.sttp_head.uMsgLength = 264;
    //sttp_data.sttp_head.uMsgLengthType = 210;
    
    snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnId.c_str());
    
    sttp_data.sttp_body.variant_member.file_data.strFileName="/CONFIG/config.info";
    sttp_data.sttp_body.variant_member.file_data.nOffset=0;
    sttp_data.sttp_body.variant_member.file_data.strReport="/CONFIG/";//ip
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
int CXJPro103ClientWay::CallModelXmlFile()
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 210;
    sttp_data.sttp_head.uMsgType = 'I';
    //sttp_data.sttp_head.uMsgLength = 264;
    //sttp_data.sttp_head.uMsgLengthType = 210;
    
    snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnId.c_str());
    
    sttp_data.sttp_body.variant_member.file_data.strFileName="/MODEL/model.xml";
    sttp_data.sttp_body.variant_member.file_data.nOffset=0;
    sttp_data.sttp_body.variant_member.file_data.strReport="/MODEL/";//ip
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}

int CXJPro103ClientWay:: CallModelXmlFile(const std::string &fileName)
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 210;
    sttp_data.sttp_head.uMsgType = 'I';
    //sttp_data.sttp_head.uMsgLength = 264;
    //sttp_data.sttp_head.uMsgLengthType = 210;
    
    snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnId.c_str());
    
    sttp_data.sttp_body.variant_member.file_data.strFileName=fileName;
    sttp_data.sttp_body.variant_member.file_data.nOffset=0;
    sttp_data.sttp_body.variant_member.file_data.strReport="/MODEL/";//ip
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}
    

 int CXJPro103ClientWay::CallModelFileList()
 {
    m_rLogFile.FormatAdd(CLogFile::trace,"----------[%s][CXJPro103ClientWay::CallModelFileList] 开始召读文件模型文件列表");
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 203;
    sttp_data.sttp_head.uMsgType = 'I';
   
    snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnId.c_str());
    sttp_data.sttp_body.nEventType = 1;   // 按文件名召读
    sprintf(sttp_data.sttp_body.ch_time_20_BIT1,"0");
    sprintf(sttp_data.sttp_body.ch_time_20_BIT2,"0");
    sttp_data.sttp_body.strFilenameWithPath = "/MODEL/";
   // sttp_data.sttp_body.strFilenameWithPath = "\\MODEL\\";
 
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
     
 }
int CXJPro103ClientWay::CallAutoScdFile()
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 210;
    sttp_data.sttp_head.uMsgType = 'I';
    //sttp_data.sttp_head.uMsgLength = 264;
    //sttp_data.sttp_head.uMsgLengthType = 210;
    
    snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnId.c_str());
    
    sttp_data.sttp_body.variant_member.file_data.strFileName = m_strScdPath.c_str();
    sttp_data.sttp_body.variant_member.file_data.nOffset=0;
    sttp_data.sttp_body.variant_member.file_data.strReport="/SCD/";//ip
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
}

//config--------------------------------------------------------
int CXJPro103ClientWay::PraserCfgInfoFile(string strFile)
{
    int ret = 0;
    TiXmlDocument * doc = new TiXmlDocument();
    if(NULL==doc){
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] 创建XML文件句柄失败",m_strStnId.c_str());
        return -1;
    }
    string strFullFile=strDragonHome+strFile;
    if (doc->LoadFile(strFullFile.c_str(), TIXML_ENCODING_UTF8)){
        TiXmlNode* rootNode = doc->FirstChild();
        if(NULL != rootNode){
            TiXmlDeclaration * rootDeclaration = rootNode->ToDeclaration();
            if(NULL != rootDeclaration ){
               //printf( "[%s][%s][%s]",rootDeclaration->Encoding(),rootDeclaration->Version(),rootDeclaration->Standalone() ); 
               if( strcmp("UTF-8",__CvtUtf82Gbk(rootDeclaration->Encoding()) ) !=0  &&
                   strcmp("utf-8", __CvtUtf82Gbk(rootDeclaration->Encoding())) != 0){
                   m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] xml IS not UTF-8] ",m_strStnId.c_str());
                   delete doc;
                   return -2;
               }
            }
        }
        
        TiXmlElement * rootTree = doc->RootElement();
        if(NULL == rootTree){
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] rootTree NULL",m_strStnId.c_str());
            delete doc;
            return -3;
        }
        
        TiXmlElement * pHeader = rootTree->FirstChildElement("Header");
        if( pHeader!= NULL){
            sStnCfgInfoFile.sHeaderNow.strDesc = __CvtUtf82Gbk(pHeader->Attribute("desc"));
            sStnCfgInfoFile.sHeaderNow.strVersion = __CvtUtf82Gbk(pHeader->Attribute("version"));
            sStnCfgInfoFile.sHeaderNow.strFileCrc = __CvtUtf82Gbk(pHeader->Attribute("CRC"));
            sStnCfgInfoFile.sHeaderNow.strDate = __CvtUtf82Gbk(pHeader->Attribute("date"));
            TiXmlElement * pHeaderHis = pHeader->FirstChildElement("History");
            if( pHeaderHis!= NULL){
                for(TiXmlElement * pHitem = pHeaderHis->FirstChildElement("Hitem");pHitem!=NULL;pHitem=pHitem->NextSiblingElement("Hitem")){
                    STN_CFG_HEADER sItem;
                    sItem.strVersion = __CvtUtf82Gbk(pHitem->Attribute("version"));
                    sItem.strReVision = __CvtUtf82Gbk(pHitem->Attribute("revision"));
                    sItem.strWhen = __CvtUtf82Gbk(pHitem->Attribute("when"));
                    sItem.strWho = __CvtUtf82Gbk(pHitem->Attribute("who"));
                    sItem.strWhat = __CvtUtf82Gbk(pHitem->Attribute("what"));
                    sItem.strWhy = __CvtUtf82Gbk(pHitem->Attribute("why"));
                    sStnCfgInfoFile.setHeaderHis.insert(sItem);
                }
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] pHeaderHis err",m_strStnId.c_str());
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] Header err",m_strStnId.c_str());
        }

	SecDevFlowMoudle::getInstanse()->reset(m_strStnId);// lmy add


        TiXmlElement * pIEDList = rootTree->FirstChildElement("IEDList");
        if( pIEDList!= NULL){
            {
                CLockUp lockUp(&m_LockFor860Step);
                m_map860IedList.clear();
            }
            m_mapIedName2ChNo.clear();
            {//未解析出通道的报文，不知道通道号，又不能往别人通道缓存里塞会塞乱，只能独立通道号
                CHNO_TC_MSG vNull;
                CLockUp lockUp(&m_LockForSendHnTcMsgList);
                m_mapNoTcMsg.insert(make_pair(0,vNull));//借用0作为未知通道缓冲区
            }
            
            for(TiXmlElement * pDevice = pIEDList->FirstChildElement("Device");pDevice!=NULL;pDevice=pDevice->NextSiblingElement("Device")){
                STN_DEV_TUNNEL sItem;
                sItem.strNo = __CvtUtf82Gbk(pDevice->Attribute("no"));
                sItem.strChNum = __CvtUtf82Gbk(pDevice->Attribute("ch_num"));
                sItem.strDevName = __CvtUtf82Gbk(pDevice->Attribute("name"));
                sItem.strIedAddr = __CvtUtf82Gbk(pDevice->Attribute("addr"));
                sItem.strChPara = __CvtUtf82Gbk(pDevice->Attribute("ch_para"));
                sItem.strChType = __CvtUtf82Gbk(pDevice->Attribute("ch_type"));
                sItem.strPType = __CvtUtf82Gbk(pDevice->Attribute("p_type"));
                sItem.strIedName = __CvtUtf82Gbk(pDevice->Attribute("ied_name"));
                sStnCfgInfoFile.setDevList.insert(sItem);
                
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserCfgInfoFile()]m_map860IedList ied_name[%s] no[%s]type[%s]",m_strStnId.c_str(), 
                           sItem.strIedName.c_str(), sItem.strNo.c_str(), sItem.strPType.c_str());
                   
                if(sItem.strChPara.empty()){
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserCfgInfoFile()]m_map860IedList ied_name[%s] no[%s]type[%s] ch_para[%s]ip为空,不做管理，不建连",m_strStnId.c_str(), 
                           sItem.strIedName.c_str(), sItem.strNo.c_str(), sItem.strPType.c_str(),sItem.strChPara.c_str());
                    continue;
                }

//if(sItem.strNo!="4") continue;
                if(0==strcmp(sItem.strPType.c_str(),"1:DL/T860"))
                {
                    STEP_INFO s;
                    s.nStep = LinkUnknow;
                    s.strIedName = sItem.strIedName.c_str();
                    int nChNo=atoi(sItem.strChNum.c_str());
                    {
                        CLockUp lockUp(&m_LockFor860Step);
                        m_map860IedList.insert(make_pair(nChNo,s));
                    }
                    {//上前置的缓冲区
                        CHNO_TC_MSG vNull;
                        CLockUp lockUp(&m_LockForSendHnTcMsgList);
                        m_mapNoTcMsg.insert(make_pair(nChNo,vNull));
                    }
                    {
                        set<u_short> sNull;
                        CLockUp lockUp(&m_LockForSend40003Sttp);
                        m_mapSttpCloseChNoRii.insert(make_pair(nChNo,sNull));
                    }
                    {//下设备的缓冲区
                        RCV_TC_INFO sNull;
                        CLockUp lockUp(&m_LockForRcvHnTcMsgChNoList);
                        m_mapRcvHnTcMsg.insert(make_pair(nChNo,sNull));
                    }
                    {
                        m_mapIedName2ChNo.insert(make_pair(sItem.strIedName,nChNo));
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserCfgInfoFile()]m_mapIedName2ChNo ied_name[%s] no[%s]type[%s]",m_strStnId.c_str(), 
                          sItem.strIedName.c_str(), sItem.strNo.c_str(), sItem.strPType.c_str());
                    }
                }
		else  // 添加103配置
		{
		    SecDevFlowMoudle::getInstanse()->addSecDev(m_strStnId,sItem.strChNum,sItem.strIedName,sItem.strPType); // lmy add
                     m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserCfgInfoFile()]SecDevFlowMoudle 103 ied_name[%s] no[%s]type[%s]",m_strStnId.c_str(), 
                          sItem.strIedName.c_str(), sItem.strNo.c_str(), sItem.strPType.c_str());
		}
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] pIEDList err",m_strStnId.c_str());
        }
        TiXmlElement * pWhiteList = rootTree->FirstChildElement("WhiteList");
        if( pWhiteList!= NULL){
            for(TiXmlElement * pItem = pWhiteList->FirstChildElement("item");pItem!=NULL;pItem=pItem->NextSiblingElement("item")){
                STN_WHITE sItem;
                sItem.strNo = __CvtUtf82Gbk(pItem->Attribute("no"));
                sItem.strIP = __CvtUtf82Gbk(pItem->Attribute("ip"));
                sStnCfgInfoFile.setWhiteList.insert(sItem);
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile] WhiteList err",m_strStnId.c_str());
        }
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserCfgInfoFile]praser[%s]ok",m_strStnId.c_str(),strFile.c_str());
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserCfgInfoFile]LoadFile[%s]失败",m_strStnId.c_str(),strFile.c_str());
        ret= -6;
    }
    
    delete doc;
    return ret;
}
//config---------------------------------------------------------------------

//db相关
int CXJPro103ClientWay::DbGetCh2PtByConfig()
{
    //m_mapCh2DevId  m_mapDevIdCh2
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DbGetCh2PtByConfig] setDevList[%d]",m_strStnId.c_str(), sStnCfgInfoFile.setDevList.size());
    map<string,string> mapIedNamePtId;//<ied,ptid>
    GetPtIedNameFromDB(mapIedNamePtId);
    map<int, string> mapIedChNumPtId;//<ied,ptid>
    GetPtIedChNumFromDB(mapIedChNumPtId);
    
    m_mapCh2DevId.clear();//<通道号No[config.ini],ptid>
    for(set<STN_DEV_TUNNEL >::iterator it=sStnCfgInfoFile.setDevList.begin();it!=sStnCfgInfoFile.setDevList.end();it++){
        /*if(0!=strcmp(it->strPType.c_str(),"1:DL/T860")){
            continue;
        }*/
        int nNo = atoi(it->strNo.c_str());
        if(nNo!=0){
            if (0 == strcmp(it->strPType.c_str(), "1:DL/T860"))
            {
                map<string, string>::iterator itPt = mapIedNamePtId.find(it->strIedName.c_str());
                if (itPt != mapIedNamePtId.end()) {
                    m_mapCh2DevId.insert(make_pair(nNo, itPt->second));
                    m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里ied[%s]通道[%s] db里找到了 ptid[%s]", m_strStnId.c_str(),
                        it->strIedName.c_str(), it->strNo.c_str(), itPt->second.c_str());
                }
                else {
                    m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里ied[%s]通道[%s]没在db里找到ptid", m_strStnId.c_str(), it->strIedName.c_str(), it->strNo.c_str());
                    continue;
                }
            }
            else
            {
                map<int, string>::iterator itPt = mapIedChNumPtId.find(nNo);
                if (itPt != mapIedChNumPtId.end()) {
                    m_mapCh2DevId.insert(make_pair(nNo, itPt->second));
                    m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里ied[%s]通道[%s] db里找到了 ptid[%s]", m_strStnId.c_str(),
                        it->strIedName.c_str(), it->strNo.c_str(), itPt->second.c_str());
                }
                else {
                    m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里ied[%s]通道[%s]没在db里找到ptid", m_strStnId.c_str(), it->strIedName.c_str(), it->strNo.c_str());
                    continue;
                }
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里ied[%s]通道[%s]错误",m_strStnId.c_str(),it->strIedName.c_str(),it->strNo.c_str());
            continue;
        }
    }
    
    m_mapDevIdCh2.clear();//<ptid,通道号No[config.ini]>
    for(map<int,string>::iterator it=m_mapCh2DevId.begin();it!=m_mapCh2DevId.end();it++){
        m_mapDevIdCh2.insert(make_pair(it->second,it->first));
    }
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DbGetCh2PtByConfig]config.ini里No  从db更新  m_mapCh2DevId[%d]条 m_mapDevIdCh2[%d]条",m_strStnId.c_str(), m_mapCh2DevId.size(),m_mapDevIdCh2.size());
    
    return 0;
}
int CXJPro103ClientWay::GetPtIedNameFromDB(map<string,string> &mapIedNamePtId)
{
    if ( NULL == m_pDBAcess ) {
		m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetPtIedNameFromDB]数据库操作对象指针为空。",m_strStnId.c_str());
		return -1;
	}
	CMemSet pMemSet;
	SQL_DATA SqlDate;
	SqlDate.Conditionlist.clear();
	SqlDate.Fieldlist.clear();
    
    char lsStr[50] = "";
    sprintf(lsStr,"STATION_ID = '%s' ",m_strStnId.c_str()); 
    AddCondition(SqlDate,lsStr);
    
        
	AddField(SqlDate,"61850SERVER_NAME",EX_STTP_DATA_TYPE_STRING);
    AddField(SqlDate,"PT_ID",EX_STTP_DATA_TYPE_STRING);

	char sError[255] = "";
	try
	{
		if (true == m_pDBAcess->Select(EX_STTP_INFO_SECDEV_CFG,SqlDate,sError,&pMemSet)) {
            if (0 != pMemSet.GetMemRowNum()) {
                pMemSet.MoveFirst();
                for ( int i=0;i<pMemSet.GetMemRowNum();i++) {
                    string strIedName = pMemSet.GetValue(UINT(0));
                    string strPtId = pMemSet.GetValue(UINT(1));
                    map<string,string>::iterator ita = mapIedNamePtId.find(strIedName);
                    if(ita == mapIedNamePtId.end()){
                        mapIedNamePtId.insert( make_pair(strIedName,strPtId) );
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetPtIedNameFromDB]找到ied[%s]ptid[%s]",m_strStnId.c_str(),strIedName.c_str(),strPtId.c_str());
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetPtIedNameFromDB]重复 找到ied[%s]ptid[%s]",m_strStnId.c_str(),strIedName.c_str(),strPtId.c_str());
                    }
                    pMemSet.MoveNext();
                }
                return 0;
            }else{
                return 0;
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetPtIedNameFromDB]select成功但是结果数量[%d]为0 异常",m_strStnId.c_str(),pMemSet.GetMemRowNum());
            }
		}
		else{
			m_rLogFile.FormatAdd(CLogFile::error,sError);
			return -1;
		}
	}
	catch (...)
	{
		sprintf(sError,"[%s][CTaskMngr::CheckFaultshortenDataFromDB]读取信息时发生异常.",m_strStnId.c_str());
		m_rLogFile.FormatAdd(CLogFile::error,sError);
		return -2;
	}
	return -3;
}

int CXJPro103ClientWay::GetPtIedChNumFromDB(map<int,string>& mapIedChNumPtId)
{
    if (NULL == m_pDBAcess) {
        m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::GetPtIedChNumFromDB]数据库操作对象指针为空。", m_strStnId.c_str());
        return -1;
    }
    CMemSet pMemSet;
    SQL_DATA SqlDate;
    SqlDate.Conditionlist.clear();
    SqlDate.Fieldlist.clear();

    char lsStr[50] = "";
    sprintf(lsStr, "STATION_ID = '%s' ", m_strStnId.c_str());
    AddCondition(SqlDate, lsStr);


    AddField(SqlDate, "CH_NUM", EX_STTP_DATA_TYPE_STRING);
    AddField(SqlDate, "PT_ID", EX_STTP_DATA_TYPE_STRING);

    char sError[255] = "";
    try
    {
        if (true == m_pDBAcess->Select(EX_STTP_INFO_SECDEV_CFG, SqlDate, sError, &pMemSet)) {
            if (0 != pMemSet.GetMemRowNum()) {
                pMemSet.MoveFirst();
                for (int i = 0; i < pMemSet.GetMemRowNum(); i++) {
                    int nChNum = atoi(pMemSet.GetValue(UINT(0)));
                    string strPtId = pMemSet.GetValue(UINT(1));
                    map<int,string>::iterator ita = mapIedChNumPtId.find(nChNum);
                    if (ita == mapIedChNumPtId.end()) {
                        mapIedChNumPtId.insert(make_pair(nChNum, strPtId));
                        m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::GetPtIedChNumFromDB]找到ied[chNum:%d]ptid[%s]", m_strStnId.c_str(), nChNum, strPtId.c_str());
                    }
                    else {
                        m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::GetPtIedChNumFromDB]重复 找到ied[chNum:%d]ptid[%s]", m_strStnId.c_str(), nChNum, strPtId.c_str());
                    }
                    pMemSet.MoveNext();
                }
                return 0;
            }
            else {
                return 0;
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetPtIedNameFromDB]select成功但是结果数量[%d]为0 异常",m_strStnId.c_str(),pMemSet.GetMemRowNum());
            }
		}
		else{
			m_rLogFile.FormatAdd(CLogFile::error,sError);
			return -1;
		}
	}
	catch (...)
	{
		sprintf(sError,"[%s][CTaskMngr::CheckFaultshortenDataFromDB]读取信息时发生异常.",m_strStnId.c_str());
		m_rLogFile.FormatAdd(CLogFile::error,sError);
		return -2;
	}
	return -3;
}

int CXJPro103ClientWay::DbGetLd2IedName()
{
    m_mapLd2IedName.clear();
    GetLD2IedNameFromDB(m_mapLd2IedName);
    return 0;
}
int CXJPro103ClientWay::GetLD2IedNameFromDB(map<string,string> &mapLd2IedName)
{
    if ( NULL == m_pDBAcess ) {
		m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetLD2IedNameFromDB]数据库操作对象指针为空。",m_strStnId.c_str());
		return -1;
	}
	CMemSet pMemSet;
	SQL_DATA SqlDate;
	SqlDate.Conditionlist.clear();
	SqlDate.Fieldlist.clear();
    
    char lsStr[50] = "";
    sprintf(lsStr,"STATION_ID = '%s' ",m_strStnId.c_str()); 
    AddCondition(SqlDate,lsStr);
    
        
	AddField(SqlDate,"SERVER_61850",EX_STTP_DATA_TYPE_STRING);
    AddField(SqlDate,"LD_REF",EX_STTP_DATA_TYPE_STRING);

	char sError[255] = "";
	try
	{
		if (true == m_pDBAcess->Select(EX_STTP_DEV_61850_BASE_CFG,SqlDate,sError,&pMemSet)) {
            if (0 != pMemSet.GetMemRowNum()) {
                pMemSet.MoveFirst();
                for ( int i=0;i<pMemSet.GetMemRowNum();i++) {
                    string strIedName = pMemSet.GetValue(UINT(0));//pl103
                    string strIEDLd = pMemSet.GetValue(UINT(1));//pl103ld0
                    map<string,string>::iterator ita = mapLd2IedName.find(strIEDLd);
                    if(ita == mapLd2IedName.end()){
                        mapLd2IedName.insert( make_pair(strIEDLd,strIedName) );
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetLD2IedNameFromDB]找到ied[%s]LD[%s]",m_strStnId.c_str(),strIedName.c_str(),strIEDLd.c_str());
                    }
                    pMemSet.MoveNext();
                }
                return 0;
            }else{
                return 0;
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::GetLD2IedNameFromDB]select成功但是结果数量[%d]为0 异常",m_strStnId.c_str(),pMemSet.GetMemRowNum());
            }
		}
		else{
			m_rLogFile.FormatAdd(CLogFile::error,sError);
			return -1;
		}
	}
	catch (...)
	{
		sprintf(sError,"[%s][CXJPro103ClientWay::GetLD2IedNameFromDB]读取信息时发生异常.",m_strStnId.c_str());
		m_rLogFile.FormatAdd(CLogFile::error,sError);
		return -2;
	}
	return -3;
}


int CXJPro103ClientWay::PraserModelXmlFile(string strFile)
{
    TiXmlDocument * doc = new TiXmlDocument();
    if(NULL==doc){
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile] 创建XML文件句柄失败",m_strStnId.c_str());
    }
    string strFullFile=strDragonHome+strFile;
    if (doc->LoadFile(strFullFile.c_str(), TIXML_ENCODING_UTF8)){
        TiXmlNode* rootNode = doc->FirstChild();
        if(NULL != rootNode){
            TiXmlDeclaration * rootDeclaration = rootNode->ToDeclaration();
            if(NULL != rootDeclaration ){
               //printf( "[%s][%s][%s]",rootDeclaration->Encoding(),rootDeclaration->Version(),rootDeclaration->Standalone() ); 
               if( (strcmp("UTF-8",__CvtUtf82Gbk(rootDeclaration->Encoding()))!=0) && (strcmp("utf-8",__CvtUtf82Gbk(rootDeclaration->Encoding()))!=0) ){
                   m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile] xml IS not UTF-8 or utf-8] ",m_strStnId.c_str());
                  // delete doc;
                  // return -2;
               }
            }
        }
        
        TiXmlElement * rootTree = doc->RootElement();
        if(NULL == rootTree){
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile] rootTree NULL",m_strStnId.c_str());
            delete doc;
            return -2;
        }else{
            sStnMdlInfoFile.sHeaderNow.strVersion = __CvtUtf82Gbk(rootTree->Attribute("version"));
            sStnMdlInfoFile.sHeaderNow.strFileCrc = __CvtUtf82Gbk(rootTree->Attribute("CRC"));
            sStnMdlInfoFile.sHeaderNow.strDate = __CvtUtf82Gbk(rootTree->Attribute("date"));
        }
        TiXmlElement * pGroups = rootTree->FirstChildElement("Groups");
        if( pGroups!= NULL){
            for(TiXmlElement * pGroup = pGroups->FirstChildElement("Group");pGroup!=NULL;pGroup=pGroup->NextSiblingElement("Group")){
                STN_MODEL_GROUP sGrp;
                sGrp.strId = __CvtUtf82Gbk(pGroup->Attribute("id"));
                sGrp.strDesc = __CvtUtf82Gbk(pGroup->Attribute("desc"));
                map<string,STN_MODEL_ITEM> mapItem;
                mapItem.clear();
                
                for(TiXmlElement * pHitem = pGroup->FirstChildElement("Item");pHitem!=NULL;pHitem=pHitem->NextSiblingElement("Item")){
                    STN_MODEL_ITEM sItem;
                    sItem.strId = __CvtUtf82Gbk(pHitem->Attribute("id"));
                    sItem.strDesc = __CvtUtf82Gbk(pHitem->Attribute("desc"));
                    sItem.strDataTyp = __CvtUtf82Gbk(pHitem->Attribute("Datatyp"));
                    mapItem.insert(make_pair(sItem.strId,sItem));
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserModelXmlFile] insert item id[%s]desc[%s]typ[%s]",m_strStnId.c_str(),sItem.strId.c_str(),sItem.strDesc.c_str(),sItem.strDataTyp.c_str());
                }
                map<STN_MODEL_GROUP, map<string,STN_MODEL_ITEM > >::iterator it=sStnMdlInfoFile.mapInfo.find(sGrp);
                if(it == sStnMdlInfoFile.mapInfo.end()){
                    sStnMdlInfoFile.mapInfo.insert(make_pair(sGrp,mapItem));
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserModelXmlFile] insert grp id[%s]desc[%s]",m_strStnId.c_str(),sGrp.strId.c_str(),sGrp.strDesc.c_str());
                }else{
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile] sGrp[%s]same  err",m_strStnId.c_str(),sGrp.strId.c_str());
                    it->second = mapItem;
                }
            }
        }else{
            m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile] pGroups err");
        }
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PraserModelXmlFile]praser[%s]ok",m_strStnId.c_str(),strFile.c_str());
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::PraserModelXmlFile]LoadFile[%s]失败",m_strStnId.c_str(),strFile.c_str());
    }
    delete doc;
}

int CXJPro103ClientWay::Read40002Ver(STTP_FULL_DATA &sttp_data)
{
    vector<BYTE> vBytes;
    for ( std::string::iterator it=sttp_data.sttp_body.strMessage.begin(); it!=sttp_data.sttp_body.strMessage.end(); ++it){
        vBytes.insert(vBytes.end(),1,*it);
    }
    {
        string str1(vBytes.begin(),vBytes.begin()+100);
        sStnVerInfo.strDevMdl=str1.c_str();//字符串，共100个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 装置型号[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+100,vBytes.begin()+110);
        sStnVerInfo.strDevVer=str1.c_str();//字符串，共10个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 装置版本[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+110,vBytes.begin()+126);
        sStnVerInfo.strDevVerCrc=str1.c_str();//字符串，共16个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 版本校验码[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+126,vBytes.begin()+133);
        CXJTime pTime;
        pTime.AssignTimeString(str1,CXJTime::CP56Time2a);
        string strTime = pTime.GetTimeString(CXJTime::STTP15Time);
        sStnVerInfo.strDevVerTime=strTime.c_str();//CP56Time2a=7byte
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 版本生成时间[%s]",m_strStnId.c_str(),strTime.c_str());
    }
    {
        string str1(vBytes.begin()+133,vBytes.begin()+143);
        sStnVerInfo.strCfgInfoVer=str1.c_str();//字符串，共10个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 配置文件(config.info)版本[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+143,vBytes.begin()+159);
        sStnVerInfo.strCfgInfoCrc=str1.c_str();//字符串，共16个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 配置文件CRC[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+159,vBytes.begin()+166);
        CXJTime pTime;
        pTime.AssignTimeString(str1,CXJTime::CP56Time2a);
        string strTime = pTime.GetTimeString(CXJTime::STTP15Time);
        sStnVerInfo.strDevVerTime=strTime.c_str();//CP56Time2a=7byte
        sStnVerInfo.strCfgInfoTime=strTime.c_str();//CP56Time2a
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 配置文件生成时间[%s]",m_strStnId.c_str(),strTime.c_str());
    }
    {
        string str1(vBytes.begin()+166,vBytes.begin()+176);
        sStnVerInfo.strMdlXmlVer=str1.c_str();//字符串，共10个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 模型文件(Model.xml)版本[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+176,vBytes.begin()+192);
        sStnVerInfo.strMdlXmlCrc=str1.c_str();//字符串，共16个字节 未使用字节补0
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 模型文件CRC[%s]",m_strStnId.c_str(),str1.c_str());
    }
    {
        string str1(vBytes.begin()+192,vBytes.begin()+199);
        CXJTime pTime;
        pTime.AssignTimeString(str1,CXJTime::CP56Time2a);
        string strTime = pTime.GetTimeString(CXJTime::STTP15Time);
        sStnVerInfo.strMdlXmlTime=strTime.c_str();//CP56Time2a
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Ver]子站 模型文件生成时间[%s]",m_strStnId.c_str(),strTime.c_str());
    }
    
}
int CXJPro103ClientWay::Read40002Item(STTP_FULL_DATA &sttp_data)
{//只会一组
    vector<BYTE> vBytes;
    for ( std::string::iterator it=sttp_data.sttp_body.strMessage.begin(); it!=sttp_data.sttp_body.strMessage.end(); ++it){
        vBytes.insert(vBytes.end(),1,*it);
    }
    
    STN_MODEL_GROUP sGrp;
    char c50[50];
    vector<BYTE>::iterator itCur=vBytes.begin();
    {//组号1byte
        bzero(c50,50);
        snprintf(c50,sizeof(c50),"%d",*itCur);
        sGrp.strId = c50;//
        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Item] 上送grp [%d]ID[%s]",m_strStnId.c_str(),*itCur,sGrp.strId.c_str());
        itCur++;
    }
    int nItems=0;
    {//条目数量1byte
        nItems = *itCur;
        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Item] 上送grp [%d]条目数量[%d]",m_strStnId.c_str(),*itCur,nItems);
        itCur++;
    }
    
    map<string,STN_MODEL_ITEM> mapVals;//条目结果
    for(int i =0;i<nItems;i++){
        STN_MODEL_ITEM s1;
        {//条目号1编号（2个字节）
            unsigned char nL,nH;
            nL = *itCur;itCur++;
            nH = *itCur;itCur++;
            int nItem = nL + nH * 0x100;
            bzero(c50,50);
            snprintf(c50,sizeof(c50),"%d",nItem);
            s1.strId = c50;//
           //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Item] 上送 item L[%d]H[%d] item ID[%s]",m_strStnId.c_str(),nL,nH,s1.strId.c_str());
        }
        {//条目号1数据长度（1个字节）
            unsigned char nLen = *itCur;itCur++;
            bzero(c50,50);
            snprintf(c50,sizeof(c50),"%d",nLen);
            s1.strDataLen=c50;//
            s1.nDataLen = nLen;
           //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Item] 上送 item Len[%d] item Len[%s]",m_strStnId.c_str(),nLen,s1.strDataLen.c_str());
        }
        {
            string str1(itCur,itCur+s1.nDataLen);
            for(int a=0;a<s1.nDataLen;a++) {
                itCur++;
            }
            s1.strDataMsg.assign(str1);//
            s1.tDataRcvTime = time(NULL);
        }
        mapVals.insert(make_pair(s1.strId,s1));
    }
    
    map<STN_MODEL_GROUP, map<string,STN_MODEL_ITEM> >::iterator itG=sStnMdlInfoFile.mapInfo.find(sGrp);
    if(itG!=sStnMdlInfoFile.mapInfo.end()){
        for(map<string,STN_MODEL_ITEM>::iterator itIV=mapVals.begin();itIV!=mapVals.end();itIV++){
            map<string,STN_MODEL_ITEM>::iterator itI=itG->second.find(itIV->first);
            if(itI!=itG->second.end()){
                itI->second.strDataLen.assign( itIV->second.strDataLen );
                itI->second.strDataMsg.assign( itIV->second.strDataMsg );
                itI->second.nDataLen = itIV->second.nDataLen;
                itI->second.tDataRcvTime = itIV->second.tDataRcvTime;
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002Item()] Mdl group[%s]Item[%s] find in mdl file update val!",m_strStnId.c_str(),sGrp.strId.c_str(),itIV->first.c_str());
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Read40002Item()] Mdl group[%s]Item[%s] not find in mdl file",m_strStnId.c_str(),sGrp.strId.c_str(),itIV->first.c_str());
            }
        }
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Read40002Item()] Mdl id[%s] not find in mdl file",m_strStnId.c_str(),sGrp.strId.c_str());
    }
    //更新完值后全量上送一下sttp
    SendAllMdlData(sGrp,mapVals,true);
    return 0;
}
int CXJPro103ClientWay::Read40002ItemUp(STTP_FULL_DATA &sttp_data)
{//只会一组
    vector<BYTE> vBytes;
    for ( std::string::iterator it=sttp_data.sttp_body.strMessage.begin(); it!=sttp_data.sttp_body.strMessage.end(); ++it){
        vBytes.insert(vBytes.end(),1,*it);
    }
    
    STN_MODEL_GROUP sGrp;
    char c50[50];
    vector<BYTE>::iterator itCur=vBytes.begin();
    {//组号1byte
        bzero(c50,50);
        snprintf(c50,sizeof(c50),"%d",*itCur);
        sGrp.strId = c50;//
        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002ItemUp] 上送grp [%d]ID[%s]",m_strStnId.c_str(),*itCur,sGrp.strId.c_str());
        itCur++;
    }
    
    map<string,STN_MODEL_ITEM> mapVals;//条目结果
    STN_MODEL_ITEM s1;
    {//条目号1编号（2个字节）
        unsigned char nL,nH;
        nL = *itCur;itCur++;
        nH = *itCur;itCur++;
        int nItem = nL + nH * 0x100;
        bzero(c50,50);
        snprintf(c50,sizeof(c50),"%d",nItem);
        s1.strId = c50;//
        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002ItemUp] 上送 item L[%d]H[%d] item ID[%s]",m_strStnId.c_str(),nL,nH,s1.strId.c_str());
    }
    {//条目号1数据长度（1个字节）
        unsigned char nLen = *itCur;itCur++;
        bzero(c50,50);
        snprintf(c50,sizeof(c50),"%d",nLen);
        s1.strDataLen=c50;//
        s1.nDataLen = nLen;
       //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002ItemUp] 上送 item Len[%d] item Len[%s]",m_strStnId.c_str(),nLen,s1.strDataLen.c_str());
    }
    {
        string str1(itCur,itCur+s1.nDataLen);
        for(int a=0;a<s1.nDataLen;a++) {
            itCur++;
        }
        s1.strDataMsg.assign(str1);//
        s1.tDataRcvTime = time(NULL);
    }
    mapVals.insert(make_pair(s1.strId,s1));
    
    map<STN_MODEL_GROUP, map<string,STN_MODEL_ITEM> >::iterator itG=sStnMdlInfoFile.mapInfo.find(sGrp);
    if(itG!=sStnMdlInfoFile.mapInfo.end()){
        for(map<string,STN_MODEL_ITEM>::iterator itIV=mapVals.begin();itIV!=mapVals.end();itIV++){
            map<string,STN_MODEL_ITEM>::iterator itI=itG->second.find(itIV->first);
            if(itI!=itG->second.end()){
                itI->second.strDataLen.assign( itIV->second.strDataLen );
                itI->second.strDataMsg.assign( itIV->second.strDataMsg );
                itI->second.tDataRcvTime = itIV->second.tDataRcvTime;
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Read40002ItemUp()] Mdl group[%s]Item[%s] find in mdl file update val!",m_strStnId.c_str(),sGrp.strId.c_str(),itIV->first.c_str(),itI->second.strDataLen.c_str() );
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Read40002ItemUp()] Mdl group[%s]Item[%s] not find in mdl file",m_strStnId.c_str(),sGrp.strId.c_str(),itIV->first.c_str());
            }
        }
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Read40002ItemUp()] Mdl id[%s] not find in mdl file",m_strStnId.c_str(),sGrp.strId.c_str());
    }
    //更新完值后全量上送一下sttp
    SendAllMdlData(sGrp,mapVals,false);
    return 0;
}

int CXJPro103ClientWay::CmpCrcAndFileVal()
{
    
    
    int ret = -1;
    if ( (!sStnCfgInfoFile.sHeaderNow.strFileCrc.empty())&&(!sStnMdlInfoFile.sHeaderNow.strFileCrc.empty()) ){
        if ( (0==strcmp(sStnVerInfo.strCfgInfoCrc.c_str(),sStnCfgInfoFile.sHeaderNow.strFileCrc.c_str())) 
                && (0==strcmp(sStnVerInfo.strMdlXmlCrc.c_str(),sStnMdlInfoFile.sHeaderNow.strFileCrc.c_str())) ){
            if(m_DoCrcChk){
                m_bCanProxy = true;
            }
            sStnMdlInfoFile.bChkCrcOk = true;
            sStnCfgInfoFile.bChkCrcOk = true;
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CmpCrcAndFileVal()] MMS CRC can proxy[%s]=[%s][%s]=[%s]",m_strStnId.c_str(),
                    sStnVerInfo.strCfgInfoCrc.c_str(),sStnCfgInfoFile.sHeaderNow.strFileCrc.c_str(),
                    sStnVerInfo.strMdlXmlCrc.c_str(),sStnMdlInfoFile.sHeaderNow.strFileCrc.c_str()  ); 
            ret = 0;
        }else{
            if(m_DoCrcChk){
                m_bCanProxy = false;
            }
            if(0!=strcmp(sStnVerInfo.strCfgInfoCrc.c_str(),sStnCfgInfoFile.sHeaderNow.strFileCrc.c_str())) {
                sStnCfgInfoFile.bChkCrcOk = false;
            }
            if(0!=strcmp(sStnVerInfo.strMdlXmlCrc.c_str(),sStnMdlInfoFile.sHeaderNow.strFileCrc.c_str())){
                sStnMdlInfoFile.bChkCrcOk = false;
            }
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CmpCrcAndFileVal()]  MMS CRC can not proxy config.ini[%s]=[%s] model.xml[%s]=[%s]",m_strStnId.c_str(),
                    sStnVerInfo.strCfgInfoCrc.c_str(),sStnCfgInfoFile.sHeaderNow.strFileCrc.c_str(),
                    sStnVerInfo.strMdlXmlCrc.c_str(),sStnMdlInfoFile.sHeaderNow.strFileCrc.c_str()  ); 
        }
    }else{
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CmpCrcAndFileVal()] CRC cfgCrc[%s] Mdlcrc[%s]",m_strStnId.c_str(),
                sStnCfgInfoFile.sHeaderNow.strFileCrc.c_str(),sStnMdlInfoFile.sHeaderNow.strFileCrc.c_str());
    }
    return ret;
}
int CXJPro103ClientWay::SendAlarmStnCrcFail()
{
    //暂发20144
    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::AlarmStnCrcFail()] 20144 crc不过,cfg[%d]mdl[%d]",m_strStnId.c_str(),sStnCfgInfoFile.bChkCrcOk,sStnMdlInfoFile.bChkCrcOk);
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 20144;
    sttp_data.sttp_head.uMsgType = 'I';
    
    
    snprintf(sttp_data.sttp_body.ch_station_id,sizeof(sttp_data.sttp_body.ch_station_id),"%s",m_strStnId.c_str());
    //sttp_data.sttp_body.nFlag = 2;//2区
    sttp_data.sttp_body.nFlag = m_nWorkArea;//从配置文件读取所工作区
    sttp_data.sttp_body.nStatus = 0;//该区的通讯状态值 (0-断开 1-正常)
    
     m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::AlarmStnCrcFail()] ,工作区号:%d",sttp_data.sttp_body.nFlag);
//0-连接正常;
//1-通道备用;
//2-通道异常，无法与子站主机通信;
//3-通道异常，无法与子站网关通信;
//4-子站软件异常，无法建立网络连接;
//5-子站软件异常，初始化链路失败
//9-子站与分站断开
//10-通道异常:主子站通信链路断开
    sttp_data.sttp_body.nEventType = 5;
    {
        CXJTime CvtTime( time(NULL) );
        snprintf(sttp_data.sttp_body.ch_time_12_BIT1,sizeof(sttp_data.sttp_body.ch_time_12_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP12Time).c_str());
    }
                        
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info);
    }
    
}

//0-正常，1-停运,2-未知
int CXJPro103ClientWay::SendPtChNo20002(int nChNo,int nState)
{
    //20002-通信状态
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendPtChNo20002()]通道[%d]nState[%d] init完成  send 20002 ",m_strStnId.c_str(),nChNo,nState);
    STTP_FULL_DATA sttp_data20002;  
    zero_sttp_full_data(sttp_data20002);
    sttp_data20002.sttp_head.uMsgID = 20002;
    sttp_data20002.sttp_head.uMsgType = 'I';
    
    time_t tTTime = time(0);//如何设置值省去
    CXJTime tTime(tTTime);
    string strTime = tTime.GetTimeString(CXJTime::STTP12Time).c_str();
    snprintf(sttp_data20002.sttp_body.ch_time_12_BIT1,sizeof(sttp_data20002.sttp_body.ch_time_12_BIT1),"%s",strTime.c_str());

    STTP_DATA s1Item2;
    map<int,string>::iterator itPtID = m_mapCh2DevId.find(nChNo);
    if(itPtID != m_mapCh2DevId.end()){
        s1Item2.str_value = itPtID->second.c_str();//保护装置标识号
        if(nState==0){
            s1Item2.id = 0;//0-正常，1-停运,2-未知
            s1Item2.int_reserved = 0;
        }else if(nState==2){
            s1Item2.id = 2;//0-正常，1-停运,2-未知
            s1Item2.int_reserved = 14;
        }else{
            s1Item2.id = 1;//0-正常，1-停运,2-未知
            s1Item2.int_reserved = 14;//14-通信中断   tb_pt_commu_detail
        }
        sttp_data20002.sttp_body.variant_member.value_data.push_back(s1Item2);//20002
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendPtChNo20002()]通道[%d]状态 send 20002 报文值str_value[%s]id[%d]reason[%d]",m_strStnId.c_str(),
                nChNo,s1Item2.str_value.c_str(),s1Item2.id,s1Item2.int_reserved);
    }else{
       m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendPtChNo20002()]通道[%d]没找到ptid send 20002 失败",m_strStnId.c_str(), nChNo); 
       return -1;//发内容空的会导致数据处理空
    }

	if ( 0 == sttp_data20002.sttp_body.variant_member.value_data.size() )
	{
		return 0;
	}

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info20002; //20002通信状态
    Sttp_Info20002.sttp_data = sttp_data20002;
    Sttp_Info20002.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info20002);
    }
	return 0;
}
int CXJPro103ClientWay::SendAllPtChNo20002()
{
    //20002-通信状态
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllPtChNo20002()]  send all 20002 ",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data20002;  
    zero_sttp_full_data(sttp_data20002);
    sttp_data20002.sttp_head.uMsgID = 20002;
    sttp_data20002.sttp_head.uMsgType = 'I';
    
    time_t tTTime = time(0);//如何设置值省去
    CXJTime tTime(tTTime);
    string strTime = tTime.GetTimeString(CXJTime::STTP12Time).c_str();
    snprintf(sttp_data20002.sttp_body.ch_time_12_BIT1,sizeof(sttp_data20002.sttp_body.ch_time_12_BIT1),"%s",strTime.c_str());
    { 
        CLockUp lockUp(&m_LockFor860Step);
        for( map<int,STEP_INFO>::iterator itNo=m_map860IedList.begin();itNo!=m_map860IedList.end();itNo++ ){
            int nChNo = itNo->first;
            int nStatus = 2;//0-正常，1-停运,2-未知
            if(itNo->second.nStep==RcvInitOk){
                nStatus = 0;
            }else if(itNo->second.nStep==LinkUnknow){
                nStatus = 2;
            }else{
                nStatus = 1;
            }
            STTP_DATA s1Item2;
            map<int,string>::iterator itPtID = m_mapCh2DevId.find(nChNo);
            if(itPtID != m_mapCh2DevId.end()){
                s1Item2.str_value = itPtID->second.c_str();//保护装置标识号
                if(nStatus==0){
                    s1Item2.id = 0;//0-正常，1-停运,2-未知
                    s1Item2.int_reserved = 0;
                }else if(nStatus==2){
                    s1Item2.id = 2;//0-正常，1-停运,2-未知
                    s1Item2.int_reserved = 14;
                }else{
                    s1Item2.id = 1;//0-正常，1-停运,2-未知
                    s1Item2.int_reserved = 14;//14-通信中断   tb_pt_commu_detail
                }
                sttp_data20002.sttp_body.variant_member.value_data.push_back(s1Item2);//20002
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllPtChNo20002()]通道[%d]状态 send 20002 报文值str_value[%s]id[%d]reason[%d]",m_strStnId.c_str(),
                        nChNo,s1Item2.str_value.c_str(),s1Item2.id,s1Item2.int_reserved);
            }else{
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllPtChNo20002()]通道[%d]没找到ptid send 20002失败",m_strStnId.c_str(),nChNo);
            }
        }
    }
    if(sttp_data20002.sttp_body.variant_member.value_data.size()==0){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllPtChNo20002()]内容空，不发20002退出",m_strStnId.c_str());
        return -1;
    }

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info20002; //20002通信状态
    Sttp_Info20002.sttp_data = sttp_data20002;
    Sttp_Info20002.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info20002);
    }
}
int CXJPro103ClientWay::SendFile20531(STTP_FULL_DATA &sttp_data,int nRcvScd)
{
    //20531-文件召唤结果报文
    //m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendFile20531()]厂站scd文件自动召唤 send 20531 ",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data20531;  
    zero_sttp_full_data(sttp_data20531);
    sttp_data20531.sttp_head.uMsgID = 20531;
    sttp_data20531.sttp_head.uMsgType = 'I';
    sttp_data20531.sttp_head.uMsgLengthType = nRcvScd;
    
    time_t tTTime = time(0);//如何设置值省去
    CXJTime tTime(tTTime);
    string strTime = tTime.GetTimeString(CXJTime::STTP12Time).c_str();
    snprintf(sttp_data20531.sttp_body.ch_time_12_BIT1,sizeof(sttp_data20531.sttp_body.ch_time_12_BIT1),"%s",strTime.c_str());
    
    snprintf(sttp_data20531.sttp_body.ch_station_id,sizeof(sttp_data20531.sttp_body.ch_station_id),"%s",m_strStnId.c_str());
    //snprintf(sttp_data20531.sttp_body.ch_pt_id,sizeof(sttp_data20531.sttp_body.ch_pt_id),"%s",strTime.c_str());
    sttp_data20531.sttp_body.nFlag = 2;//1-文件列表；2-文件
    sttp_data20531.sttp_body.nEventType = 2;//2-SCD
    if(nRcvScd == RcvScdChg20531){
        sttp_data20531.sttp_body.nStatus = 1;//0-失败 1-成功
        sttp_data20531.sttp_body.strFilenameWithPath = m_strScdPath.c_str();       
    }else{
        sttp_data20531.sttp_body.nStatus = sttp_data.sttp_body.nStatus;//0-失败 1-成功
        sttp_data20531.sttp_body.strFilenameWithPath = sttp_data.sttp_body.variant_member.file_data.strFileName.c_str();
    }
    

    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendFile20531()]厂站scd文件自动召唤 send 20531, pathFile[%s] status[%d][0-失败 1-成功]",m_strStnId.c_str(), 
                    sttp_data20531.sttp_body.strFilenameWithPath.c_str(),sttp_data20531.sttp_body.nStatus); 


    BUS_RECV_STTPFULLDATA_INFO Sttp_Info20531; //20531
    Sttp_Info20531.sttp_data = sttp_data20531;
    Sttp_Info20531.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info20531);
    }
}


int CXJPro103ClientWay::SendAllMdlData(STN_MODEL_GROUP &sGrpId,map<string,STN_MODEL_ITEM> &mapItemVal,bool bIsRespond)
{
    map<STN_MODEL_GROUP, map<string,STN_MODEL_ITEM> >::iterator itG=sStnMdlInfoFile.mapInfo.find(sGrpId);
    if(itG!=sStnMdlInfoFile.mapInfo.end()){
        int nGrp=atoi(sGrpId.strId.c_str());
        CXJTime CvtTime( time(NULL) );
        
        if(1==nGrp){
            //通道状态   开关量  3.25  保护开关量采样值上载通知(20010)
            
            //20010-开关量--放前面因为下面要push内容
            STTP_FULL_DATA sttp_data;  //20010
            zero_sttp_full_data(sttp_data);
            sttp_data.sttp_head.uMsgID = 20010;
            sttp_data.sttp_head.uMsgType = 'I';
            snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnMgrSecDev.c_str());
            sttp_data.sttp_body.nCpu = 1;
            
            for(map<string,STN_MODEL_ITEM>::iterator itItem=mapItemVal.begin();itItem!=mapItemVal.end();itItem++){
                //模型<item.info>
                map<string,STN_MODEL_ITEM>::iterator itM=itG->second.find(itItem->first.c_str());
                if(itM!=itG->second.end()){
					int nChNo = -1;
					int nStatus = 0;
                    STTP_DATA s1Item;
                    int nItem = atoi(itItem->second.strId.c_str());
                    int nId = 1*1000000 + 1000*nGrp + nItem;//子站管理单元建模Di-ID=组号*1000+条目号+cpu*1000000
                    s1Item.id = nId;
                    string strTime;//如果类型18-得到时间
                    GetStrValByType(itM->second.strDataTyp, itItem->second.strDataMsg, s1Item.str_value, strTime);
                    if(s1Item.str_value == "0"){
                         nChNo = atoi(itItem->first.c_str());
                         m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道号[%d] 连接关闭",m_strStnId.c_str(), nChNo);
						 nStatus = 1;
                        // lmy add 通道状态变化
                         SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,nChNo,1); // CLOSE_CONNECT
                         //SendPtChNo20002(nChNo,1);
                    }

                    //通道号在状态机队列中查找到标识该通道是执行透传61850协议
                    //61850协议，只有收到状态变位为断开且为initOk完成状态才发送20002报文
                    map<int, STEP_INFO>::iterator iterIed =m_map860IedList.find(nChNo);
                    if (iterIed != m_map860IedList.end())
                    {
                        if (RcvInitOk == iterIed->second.nStep && s1Item.str_value == "0")
                        {
                            SendPtChNo20002(nChNo, nStatus);
                        }
                    }
                    else
                    {
                        //值未变位不发送
                        if (0 != strcmp(itM->second.strDataMsgTrans.c_str(), s1Item.str_value.c_str())) {
                            m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::SendAllMdlData()]通道状态grp[%s]item[%s] send 20010 ID[%d]val[%s]之前val[%s],发生变位,准备上送,time[%s]", m_strStnId.c_str(),
                                sGrpId.strId.c_str(), itItem->first.c_str(), s1Item.id, s1Item.str_value.c_str(), itM->second.strDataMsgTrans.c_str(), strTime.c_str());
                        }
                        else {
                            m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::SendAllMdlData()]通道状态grp[%s]item[%s] send 20010 ID[%d]val[%s]之前val[%s],未变位,不上送,time[%s]", m_strStnId.c_str(),
                                sGrpId.strId.c_str(), itItem->first.c_str(), s1Item.id, s1Item.str_value.c_str(), itM->second.strDataMsgTrans.c_str(), strTime.c_str());
                            continue;
                        }
                        //SendPtChNo20002(nChNo, nStatus);
                    }

                    itM->second.strDataMsgTrans = s1Item.str_value;//更新模型中值
                    if( 0==strcmp(itM->second.strDataTyp.c_str(),"18") ){// 18=带时标的报文 1by+7by+1by
                        itM->second.strDataTime = strTime;////如果18类型的,保留下数据里的时间
                    }
                    
                    
                    if(bIsRespond){
                        string strTimeStn = strTime.c_str();
                        strTime = CvtTime.GetTimeString(CXJTime::STTP15Time).c_str();
                        sttp_data.sttp_body.variant_member.value_data.push_back(s1Item);//20010
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道状态grp[%s]item[%s] send 20010 ID[%d]val[%s]time[%s] 召唤回复的用主站接收时间[%s]，避免上送时间异常导致归到过老事件 ",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),strTimeStn.c_str(),strTime.c_str());
                    }else{//主动上送的，时间用上送里的值,因为时间可能不同，所以一个一条
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道状态grp[%s]item[%s] send 20010 ID[%d]val[%s]time[%s],主动上送的，所以一个一条",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),strTime.c_str());
                        //20010-开关量
                        {
                            STTP_FULL_DATA sttp_data_one;  //20010
                            zero_sttp_full_data(sttp_data_one);
                            sttp_data_one.sttp_head.uMsgID = 20010;
                            sttp_data_one.sttp_head.uMsgType = 'I';
                            snprintf(sttp_data_one.sttp_body.ch_pt_id,sizeof(sttp_data_one.sttp_body.ch_pt_id),"%s",m_strStnMgrSecDev.c_str());
                            sttp_data_one.sttp_body.nCpu = 1;
                            snprintf(sttp_data_one.sttp_body.ch_time_15_BIT1,sizeof(sttp_data_one.sttp_body.ch_time_15_BIT1),"%s",strTime.c_str());
                            sttp_data_one.sttp_body.variant_member.value_data.push_back(s1Item);//20010

                            BUS_RECV_STTPFULLDATA_INFO Sttp_Info_one; //20010开关量
                            Sttp_Info_one.sttp_data = sttp_data_one;
                            Sttp_Info_one.t_time = time(NULL);
                            {
                                CLockUp lockUp(&m_LockForSendBusList);
                                m_SendBusList.push_back(Sttp_Info_one);
                            }
                        }
                    }
                    
                    //通道管理 关闭管理//0-OFF  1- ON
                    //int nChNo = atoi(itItem->first.c_str());
                    //if(0==strcmp(s1Item.str_value.c_str(),"0")){
                    if(s1Item.str_value == "0"){
                        CLockUp lockUp(&m_LockFor860Step);
                        map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(nChNo);
                        if(itNo != m_map860IedList.end()){
                            //因为反正有超时，不用立刻退回
                            if( (itNo->second.nStep != LinkWtClose)&&(itNo->second.nStep != LinkWtConn)&&(itNo->second.nStep != WtReCC)&&(itNo->second.nStep != WtReInit)&&(itNo->second.nStep != LinkConned) ){
                                itNo->second.nStep = LinkClosed;
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道号[%d]状态 step[%d]退回LinkClosed  nFailCount[%d]",m_strStnId.c_str(),nChNo,itNo->second.nStep,itNo->second.nFailCount);
                            }else{
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道号[%d]状态 step[%d]wait状态,暂不退回LinkClosed  nFailCount[%d]",m_strStnId.c_str(),nChNo,itNo->second.nStep,itNo->second.nFailCount);
                            }
//                            itNo->second.tStepCur = time(NULL);
//                            itNo->second.tStepNextTime = time(NULL);
                            //itNo->second.nFailCount++;//不能++因为子站会不停上送
                            
                        }
                       
                        // lmy add 通道状态变化
                        SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,nChNo,1); // CLOSE_CONNECT
                    }
                    
                    if(s1Item.str_value == "0")
                    {
                         m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]通道号[%d] 连接关闭",m_strStnId.c_str(), nChNo);
                        // lmy add 通道状态变化
                        SecDevFlowMoudle::getInstanse()->setStep(m_strStnId,nChNo,1); // CLOSE_CONNECT
                    }

                }else{
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]grp1 条目id[%s]在mdl里找不到跳过",m_strStnId.c_str(),itItem->first.c_str());
                }
            }
            //周期招的，时间一致，可以合并一条20010
            if( (bIsRespond)&&(!sttp_data.sttp_body.variant_member.value_data.empty()) ){
                snprintf(sttp_data.sttp_body.ch_time_15_BIT1,sizeof(sttp_data.sttp_body.ch_time_15_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP15Time).c_str());
                BUS_RECV_STTPFULLDATA_INFO Sttp_Info; //20010开关量
                Sttp_Info.sttp_data = sttp_data;
                Sttp_Info.t_time = time(NULL);
                {
                    CLockUp lockUp(&m_LockForSendBusList);
                    m_SendBusList.push_back(Sttp_Info);
                }
            }
            
        }else
        if(2==nGrp){//子站状态   开关量 3.25  保护开关量采样值上载通知(20010)
            STTP_FULL_DATA sttp_data;  
            zero_sttp_full_data(sttp_data);
            sttp_data.sttp_head.uMsgID = 20010;
            sttp_data.sttp_head.uMsgType = 'I';
            snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnMgrSecDev.c_str());
            sttp_data.sttp_body.nCpu = 1;

            for(map<string,STN_MODEL_ITEM>::iterator itItem=mapItemVal.begin();itItem!=mapItemVal.end();itItem++){
                map<string,STN_MODEL_ITEM>::iterator itM=itG->second.find(itItem->first.c_str());
                if(itM!=itG->second.end()){
                    STTP_DATA s1Item;
                    int nItem = atoi(itItem->second.strId.c_str());
                    int nId = 1*1000000 + 1000*nGrp + nItem;//ID=组号*1000+条目号
                    s1Item.id = nId;
                    string strTime;//如果类型18-得到时间
                    GetStrValByType(itM->second.strDataTyp, itItem->second.strDataMsg, s1Item.str_value, strTime);
                    //值未变位不发送
                    if(0!=strcmp(itM->second.strDataMsgTrans.c_str(),s1Item.str_value.c_str())){
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]子站状态2 grp[%s]item[%s] send 20010 ID[%d]val[%s]之前val[%s],发生变位,准备上送,time[%s]",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),itM->second.strDataMsgTrans.c_str(),strTime.c_str());
                    }else{
                        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]子站状态2 grp[%s]item[%s] send 20010 ID[%d]val[%s]之前val[%s],未变位,不上送,time[%s]",m_strStnId.c_str(),
                        //        sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),itM->second.strDataMsgTrans.c_str(),strTime.c_str());
                        continue;
                    }
                    itM->second.strDataMsgTrans = s1Item.str_value;//更新模型中值
                    if( 0==strcmp(itM->second.strDataTyp.c_str(),"18") ){// 18=带时标的报文 1by+7by+1by
                        itM->second.strDataTime = strTime;////如果18类型的,保留下数据里的时间
                    }
                    
                    if(bIsRespond){
                        string strTimeStn = strTime.c_str();
                        strTime = CvtTime.GetTimeString(CXJTime::STTP15Time).c_str();
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]子站状态2 grp[%s]item[%s] send 20010 ID[%d]原始值 val[%s]time[%s] 召唤回复的用主站接收时间[%s] ",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),strTimeStn.c_str(),strTime.c_str());
                        sttp_data.sttp_body.variant_member.value_data.push_back(s1Item);//20010
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]子站状态2 grp[%s]item[%s] send 20010 ID[%d]原始值 val[%s]time[%s] 主动上送的，所以一个一条",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str(),strTime.c_str());
                        {
                            STTP_FULL_DATA sttp_data_one;  
                            zero_sttp_full_data(sttp_data_one);
                            sttp_data_one.sttp_head.uMsgID = 20010;
                            sttp_data_one.sttp_head.uMsgType = 'I';
                            snprintf(sttp_data_one.sttp_body.ch_pt_id,sizeof(sttp_data_one.sttp_body.ch_pt_id),"%s",m_strStnMgrSecDev.c_str());
                            sttp_data_one.sttp_body.nCpu = 1;
                            snprintf(sttp_data_one.sttp_body.ch_time_15_BIT1,sizeof(sttp_data_one.sttp_body.ch_time_15_BIT1),"%s",strTime.c_str());
                            sttp_data_one.sttp_body.variant_member.value_data.push_back(s1Item);//20010

                            BUS_RECV_STTPFULLDATA_INFO Sttp_Info_one;
                            Sttp_Info_one.sttp_data = sttp_data_one;
                            Sttp_Info_one.t_time = time(NULL);
                            {
                                CLockUp lockUp(&m_LockForSendBusList);
                                m_SendBusList.push_back(Sttp_Info_one);
                            }
                        }
                    }
                    
                    
                }else{
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]grp2 条目id[%s]在mdl里找不到跳过",m_strStnId.c_str(),itItem->first.c_str());
                }
            }
            //周期招的，时间一致，可以合并一条20010
            if( (bIsRespond)&&(!sttp_data.sttp_body.variant_member.value_data.empty()) ){
                snprintf(sttp_data.sttp_body.ch_time_15_BIT1,sizeof(sttp_data.sttp_body.ch_time_15_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP15Time).c_str());
                BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
                Sttp_Info.sttp_data = sttp_data;
                Sttp_Info.t_time = time(NULL);
                {
                    CLockUp lockUp(&m_LockForSendBusList);
                    m_SendBusList.push_back(Sttp_Info);
                }
            }
        }else
        if(3==nGrp){//子站测量信息  模拟量 3.21  保护模拟量采样值上载通知(20006)
            for(map<string,STN_MODEL_ITEM>::iterator itItem=mapItemVal.begin();itItem!=mapItemVal.end();itItem++){
                map<string,STN_MODEL_ITEM>::iterator itM=itG->second.find(itItem->first.c_str());
                if(itM!=itG->second.end()){
                    STTP_DATA s1Item;
                    int nItem = atoi(itItem->second.strId.c_str());
                    int nId = 1*1000000 + 1000*nGrp + nItem;//ID=cpu*1000000+组号*1000+条目号
                    s1Item.id = nId;
                    string strTime;//如果类型18-得到时间
                    GetStrValByType(itM->second.strDataTyp, itItem->second.strDataMsg, s1Item.str_value, strTime);
                    
                    {
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAllMdlData()]子站状态3 grp[%s]item[%s] send 20006 ID[%d]原始值 val[%s]",m_strStnId.c_str(),
                                sGrpId.strId.c_str(),itItem->first.c_str(),s1Item.id,s1Item.str_value.c_str());
                        
                        STTP_FULL_DATA sttp_data;  
                        zero_sttp_full_data(sttp_data);
                        sttp_data.sttp_head.uMsgID = 20006;
                        sttp_data.sttp_head.uMsgType = 'I';
                        snprintf(sttp_data.sttp_body.ch_pt_id,sizeof(sttp_data.sttp_body.ch_pt_id),"%s",m_strStnMgrSecDev.c_str());
                        sttp_data.sttp_body.nCpu = 1;
                        snprintf(sttp_data.sttp_body.ch_time_15_BIT1,sizeof(sttp_data.sttp_body.ch_time_15_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP15Time).c_str());
                        sttp_data.sttp_body.variant_member.value_data.push_back(s1Item);
                        
                        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
                        Sttp_Info.sttp_data = sttp_data;
                        Sttp_Info.t_time = time(NULL);
                        {
                            CLockUp lockUp(&m_LockForSendBusList);
                            m_SendBusList.push_back(Sttp_Info);
                        }
                    }
                }else{
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]grp3 条目id[%s]在mdl里找不到跳过",m_strStnId.c_str(),itItem->first.c_str());
                }
            }
        }else{
            //model.xml定死,不会出现
        }
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SendAllMdlData()]组id[%s]在mdl里找不到跳过",m_strStnId.c_str(),sGrpId.strId.c_str());
    }
    return 0;
}
int CXJPro103ClientWay::GetStrValByType(string strDataTyp,string strValMsg,string &strVal,string &strTime)
{
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),strValMsg.begin(),strValMsg.end());
   
    //m_rLogFile.FormatAdd(CLogFile::trace,"CXJPro103ClientWay::GetStrValByType strDataTyp[%s]",strDataTyp.c_str());
    //PrintBytes(strValMsg);
    
    if( 0==strcmp(strDataTyp.c_str(),"18") ){// 18=带时标的报文 1by+7by+1by
        if(vBytes.size()>0){
            unsigned char nDPI = vBytes.at(0) & 0x03;//00000001-off 2-on-00000010
            if(nDPI == 1){//off
                strVal = "0";//0-OFF  1- ON
            }else if(nDPI == 2){//on
                strVal = "1";//0-OFF  1- ON
            }
            string strCP56(vBytes.begin()+1,vBytes.begin()+8);//CP56Time2a=7byte、
            //PrintBytes(strCP56);
            CXJTime pTime;
            pTime.AssignTimeString(strCP56,CXJTime::CP56Time2a);
            strTime = pTime.GetTimeString(CXJTime::STTP15Time);//“YYMMDDmmhhss+3位毫秒数”
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetStrValByType] 18 strDataTyp[%s]nDPI[%d][1-断 2-通]转成strVal[%s][0-断1-通]time[%s]",m_strStnId.c_str(),
                   strDataTyp.c_str(),nDPI,strVal.c_str(),strTime.c_str());
        }else{
           m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetStrValByType]18 vBytes.size()[%d]",m_strStnId.c_str(),vBytes.size());
        }
    }else
    if( 0==strcmp(strDataTyp.c_str(),"7") ){// 7 =R32.23 =IEEE 标准 754 短实数
        if(vBytes.size()>3){
            float f;
            unsigned char *p = (unsigned char*)&f;
            p[0]=vBytes.at(0);
            p[1]=vBytes.at(1);
            p[2]=vBytes.at(2);
            p[3]=vBytes.at(3);
            char c50[50];bzero(c50,50);
            snprintf(c50,sizeof(c50),"%.3f",f);
            strVal = c50;
           m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetStrValByType] 7 strDataTyp[%s] float[%f] strVal[%s]",m_strStnId.c_str(), strDataTyp.c_str(), f, strVal.c_str());
        }else{
           m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::GetStrValByType]7 vBytes.size()[%d]",m_strStnId.c_str(),vBytes.size());
        }
    }else{
       m_rLogFile.FormatAdd(CLogFile::trace,"CXJPro103ClientWay::GetStrValByType strDataTyp[%s] 非18和7 暂不支持不处理",strDataTyp.c_str());
    }
    
    return 0;
}

int CXJPro103ClientWay::SendCfgPtCommClose()
{
    //因为建连未成功，只能靠自己的模型
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendCfgPtCommClose()]发送所有配置文件里设备的关闭状态20002",m_strStnId.c_str());
    
    
    STTP_FULL_DATA sttp_data20002;  
    zero_sttp_full_data(sttp_data20002);
    sttp_data20002.sttp_head.uMsgID = 20002;
    sttp_data20002.sttp_head.uMsgType = 'I';
    {
        CXJTime CvtTime( time(NULL) );
        snprintf(sttp_data20002.sttp_body.ch_time_12_BIT1,sizeof(sttp_data20002.sttp_body.ch_time_12_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP12Time).c_str());
    }
    
    
    for(map<string,int>::iterator it=m_mapDevIdCh2.begin();it!=m_mapDevIdCh2.end();it++){
        STTP_DATA s1Item2;
        s1Item2.str_value = it->first.c_str();//保护装置标识号
        s1Item2.id = 1;//0-正常，1-停运,2-未知

        sttp_data20002.sttp_body.variant_member.value_data.push_back(s1Item2);//20002
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendCfgPtCommClose()]通道状态 send 20002 str_value[%s]id[%d]",m_strStnId.c_str(),
                s1Item2.str_value.c_str(),s1Item2.id);     
    }

	if ( 0 == sttp_data20002.sttp_body.variant_member.value_data.size() )
	{
		return 0;
	}

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info20002; //20002通信状态
    Sttp_Info20002.sttp_data = sttp_data20002;
    Sttp_Info20002.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info20002);
    }
    return 0;
}


int CXJPro103ClientWay::SendPtChNo40007(int nChNo)
{
    //20002-通信状态
    m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::SendPtChNo40007()]通道[%d] 通知前置开始", m_strStnId.c_str(), nChNo);
    STTP_FULL_DATA sttp_data40007;
    zero_sttp_full_data(sttp_data40007);
    sttp_data40007.sttp_head.uMsgID = 40007;
    sttp_data40007.sttp_head.uMsgType = 'I';

    STTP_DATA s1Item;
    map<int, string>::iterator itPtID = m_mapCh2DevId.find(nChNo);
    if (itPtID != m_mapCh2DevId.end()) {
        s1Item.str_reserved = itPtID->second.c_str();//保护装置标识号
        sttp_data40007.sttp_body.variant_member.value_data.push_back(s1Item);//20002
        m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::SendPtChNo40007()]通道[%d] send 40007 报文值str_reserved[%s]", m_strStnId.c_str(),
            nChNo, s1Item.str_reserved.c_str());
    }
    else {
        m_rLogFile.FormatAdd(CLogFile::error, "[%s][CXJPro103ClientWay::SendPtChNo40007()]通道[%d]没找到ptid send 40007 失败", m_strStnId.c_str(), nChNo);
        return -1;//发内容空的会导致数据处理空
    }

    if (0 == sttp_data40007.sttp_body.variant_member.value_data.size())
    {
        return 0;
    }

    BUS_RECV_STTPFULLDATA_INFO Sttp_Info40007; //40007 通道请求发送使能通知
    Sttp_Info40007.sttp_data = sttp_data40007;
    Sttp_Info40007.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(Sttp_Info40007);
    }
    return 0;
}

//向保护
int CXJPro103ClientWay::ChkAsdu200DT(const string &strMsg,const string &strIed)
{
    int nPos;
    if(2 == isSubarray(strMsg,strIed,nPos)){
        return 0;
    }
    return -1;
}
int CXJPro103ClientWay::ChkAsdu200CC(const string &strMsg)
{

    if( (strMsg.length()==22) && ((unsigned char)strMsg.at(5)==0xd0) ){
        return 0;
    }else{
        return 1;
    }
//    string strCC;
//    strCC.assign(CC,sizeof(CC));   
//    if(1 == isSubarray(strMsg,strCC)){
//        return 0;
//    }
    return -1;
}
int CXJPro103ClientWay::ChkAsdu200Init(const string &strMsg)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ChkAsdu200Init] step-------MMS------------------------假装init完成",m_strStnId.c_str());
    return 0;
    string strInitRespon;
    strInitRespon.assign(InitRespon,sizeof(InitRespon));
    int nPos;
    PrintBytes(strInitRespon);
    if(1 == isSubarray(strMsg,strInitRespon,nPos)){
        return 0;
    }
    return -1;
}
int CXJPro103ClientWay::SendAsdu200Conn(int nChNo)
{
   //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200Conn]step",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdEstablishConnet;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}


int CXJPro103ClientWay::SendAsdu200CR(int nChNo)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200CR]step",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag =  GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = sizeof(CR);//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    sttp_data.sttp_body.strMessage.assign(CR,sizeof(CR));//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}

int CXJPro103ClientWay::SendAsdu200Conn103(int nChNo,int nPType)
{
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = nPType;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdEstablishConnet;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}


int CXJPro103ClientWay::SendAsdu200Init(int nChNo)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200Init]step",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = sizeof(InitReq);//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    sttp_data.sttp_body.strMessage.assign(InitReq,sizeof(InitReq));//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}
int CXJPro103ClientWay::SendAsdu200Tc(string &strMsg,int nChNo)
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200Tc]通道号[%d] step data[%d]",m_strStnId.c_str(),nChNo,strMsg.size());
    PrintBytes(strMsg);
    
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    //sttp_data.sttp_head.uMsgLengthType = 40001;
    //sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
    sttp_data.sttp_body.nStatus = strMsg.length();//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    sttp_data.sttp_body.strMessage = strMsg;//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}

int CXJPro103ClientWay::SendAsdu200Tc103(string &strMsg,int nChNo,int nPType)
{
	m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200Tc103]通道号[%d] step data[%d]",m_strStnId.c_str(),nChNo,strMsg.size());
	PrintBytes(strMsg);

	STTP_FULL_DATA sttp_data;  
	zero_sttp_full_data(sttp_data);

	sttp_data.sttp_head.uMsgRii = 0;
	sttp_data.sttp_head.uMsgID = 40001;
	sttp_data.sttp_head.uMsgType = 'I';
	sttp_data.sttp_head.uMsgLength = 0;
	//sttp_data.sttp_head.uMsgLengthType = 40001;
	//sttp_data.sttp_head.uMsgEndFlag = 0;

	sttp_data.sttp_body.nCpu = nChNo;//通道编号
	sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
	sttp_data.sttp_body.nSource = nPType;//规约类型
	sttp_data.sttp_body.nZone = 0;//压缩标志
	sttp_data.sttp_body.nEventType = CmdProxyPacket;//命令类型
	sttp_data.sttp_body.nStatus = strMsg.length();//数据包字节数
	sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
	sttp_data.sttp_body.strMessage = strMsg;//数据包


	BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
	Sttp_Info.sttp_data = sttp_data;
	Sttp_Info.t_time = time(NULL);

	{
		CLockUp lockUp(&m_LockForRcvBusInfList);
		m_RcvBusInfList.push_back(Sttp_Info);
	}
	return 0;
}

int CXJPro103ClientWay::SendAsdu200CloseConn(int nChNo)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200CloseConn]step",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = PType860;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdCloseConnect;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return 0;
}

void CXJPro103ClientWay::SendAsdu200Close103Conn(int nChNo,int nPType)
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200CloseConn]step",m_strStnId.c_str());
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = nChNo;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = nPType;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = CmdCloseConnect;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
    
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    return ;
}

//0-不是last要另存等全了判定转发，1-是Last可以透传,-1-判断失败
int CXJPro103ClientWay::IsHugeMMS(string &strMsg)
{
    if(strMsg.length()<6){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IsHugeMMS]mms tpkt报文长度[%d]<6 不判是否last cotp",m_strStnId.c_str(),strMsg.length());
        return -1;
    }
    BYTE cTPDU = strMsg.at(6);//0x80 or 0x00
    BYTE cLastDataUnit = cTPDU & 0x80;//1000 0000;1-yes 0-no
    
    if(cLastDataUnit == 0x80){//1-yes
        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IsHugeMMS]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 是 last cotp，可以透传",m_strStnId.c_str(),cTPDU,cLastDataUnit,strMsg.length());
        return 1;
    }else if(cLastDataUnit == 0x00){//0-no
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IsHugeMMS]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 不是 last cotp，不能透传，另存待完整后判断透传",m_strStnId.c_str(),cTPDU,cLastDataUnit,strMsg.length());
        return 0;
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::IsHugeMMS]mms cTPDU[%02X]LastFlag[%02X] tpkt报文长度[%d] 是否lastcotp 判定异常",m_strStnId.c_str(),cTPDU,cLastDataUnit,strMsg.length());
        return -1;
    }
    return -1;
}

typedef struct _TLV_STRUCT_EX
{
    char* p_T;
    int nlen_T;
    char* p_L;
    int nlen_L;
    char* p_V;
    int nlen_V;

}TLV_STRUCT_EX;

void ParseASN1_TLV_ex(const char* c_msg_buf, TLV_STRUCT_EX* p_tlv)
{
    memset(p_tlv, 0, sizeof(TLV_STRUCT_EX));
    int n_temp = 0;
    char* p_buf_tmp;
    //tag
    p_tlv->p_T = (char*)c_msg_buf;
    p_tlv->nlen_T = 1;
    //length
    p_tlv->p_L = p_tlv->p_T + 1;
    memcpy((char*)&n_temp, p_tlv->p_L, 1);
    if (n_temp & 0x80)	//长模式
    {
        //去高位
        if ((n_temp &= 0x7F) > 0)	// 定长模式的长形式
        {
            p_tlv->nlen_L = n_temp + 1; //描述长度所占用的字节数
            if (n_temp == 2)		//暂时只处理两字节整数
            {
                p_buf_tmp = p_tlv->p_L + 1;
                p_tlv->nlen_V = (unsigned char)p_buf_tmp[1] + (((unsigned char)p_buf_tmp[0]) << 8);
            }
            else
            {
                memcpy((char*)(&(p_tlv->nlen_V)), p_tlv->p_L + 1, 1);
            }
        }
        //非定长模式暂不处理
    }
    else
    {
        p_tlv->nlen_L = 1;
        memcpy((char*)(&(p_tlv->nlen_V)), p_tlv->p_L, 1);
    }
    //value
    p_tlv->p_V = p_tlv->p_L + p_tlv->nlen_L;
    return ;
}

int CXJPro103ClientWay::DecodeMMsType(const string& str)
{
    if (str.length() < 27) {
        m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::DecodeMMsType]mms file 报文长度[%d]<27 不判类型", m_strStnId.c_str(), str.length());
        return 0;
    }
    char* data = (char*)str.c_str();

    int offset = 0;
    offset += 7; //偏移TPKT和COTP
    offset += 4; //偏移01 00 01 00

    TLV_STRUCT_EX tlv;	 //ISO  
    ParseASN1_TLV_ex(data + offset, &tlv);
    offset += tlv.nlen_T + tlv.nlen_L;

    ParseASN1_TLV_ex(data + offset, &tlv);
    offset += tlv.nlen_T + tlv.nlen_L;

    ParseASN1_TLV_ex(data + offset, &tlv);
    offset += tlv.nlen_T + tlv.nlen_L + tlv.nlen_V;

    ParseASN1_TLV_ex(data + offset, &tlv);
    offset += tlv.nlen_T + tlv.nlen_L;

    ParseASN1_TLV_ex(data + offset, &tlv);
    offset += tlv.nlen_T + tlv.nlen_L;

    if ((uint8)tlv.p_T[0] == 0xa0 || (uint8)tlv.p_T[0] == 0xa1)
    {
        ParseASN1_TLV_ex(data + offset, &tlv);
        offset += tlv.nlen_T + tlv.nlen_L + tlv.nlen_V;
        if ((uint8)tlv.p_T[0] == 0x02)  //InvokeId
        {
            uint8 tag = *(data + offset);
            uint8 tag1 = *(data + offset + 1);
            if (tag == 0xbf && tag1 == 0x48) {  //文件相关
                return 2;
            }
            if ((0x9f == tag) && (0x49 == tag1)) {
                return 1; 
            }
            if ((0x9f == tag) && (0x4a == tag1)) {
                return 1; 
            }
        }
    }
    return 0;
}

//0-正常 <0-失败 1-文件相关走固定ied填充   a0 2-a1 3-bf48 4-9f49 5-9f4a
//int CXJPro103ClientWay::DecodeMMsType(const string &str)
//{
//    if(str.length()<27){
//        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file 报文长度[%d]<27 不判类型",m_strStnId.c_str(),str.length());
//        return 0;
//    }
//    BYTE cType = str.at(20);//0xa0-请求 0xa1-回复
//    //char cLen = str.at(21);//长度
//    BYTE cHasInvoke = str.at(22);//0x02-2位接下来
//    if( (cType==0xa0)||(cType==0xa1) ){
//        //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file  cType[%02x][0xa0-请求 0xa1-回复]",m_strStnId.c_str(),cType);
//        BYTE cSrv1;
//        BYTE cSrv2;
//        if(cHasInvoke==0x02){
//            BYTE cIvkLen = str.at(23);
//            int nSrv1PosOffset = 24+cIvkLen;
//            int nSrv2PosOffset = 25+cIvkLen;
//            if(cIvkLen==0x02){
//                //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file  id[%02x]id[%02x] ",m_strStnId.c_str(),str.at(24),str.at(25));
//                cSrv1 = str.at(nSrv1PosOffset);
//                cSrv2 = str.at(nSrv2PosOffset);
//            }else if(cIvkLen==0x01){
//                //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file  id[%02x] ",m_strStnId.c_str(),str.at(24));
//                cSrv1 = str.at(nSrv1PosOffset);
//                cSrv2 = str.at(nSrv2PosOffset);
//            }else if(cIvkLen==0x03){
//                cSrv1 = str.at(nSrv1PosOffset);
//                cSrv2 = str.at(nSrv2PosOffset);
//            }else{
//                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file  cIvkLen[%02x]非2和1和3",m_strStnId.c_str(),cIvkLen);
//                cSrv1 = str.at(nSrv1PosOffset);
//                cSrv2 = str.at(nSrv2PosOffset);
//                //return 0;
//            }
//        }else{
//            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DecodeMMsType]mms file  cHasInvoke[%02x]非2",m_strStnId.c_str(),cHasInvoke);
//            return 0;
//        }
//        
//        if(0xa0==cSrv1) {return 1;}//ENUM_MMS_UNKNOW
//        if(0xa1==cSrv1) {return 1;}//ENUM_MMS_GETNAMELIST
//        if( (0xbf==cSrv1)&&(0x48==cSrv2) ) {//ENUM_MMS_FILE_OPEN
//            return 2;//file-open
//        }
//        if( (0x9f==cSrv1)&&(0x49==cSrv2) ) {return 1;}//ENUM_MMS_FILE_READ
//        if( (0x9f==cSrv1)&&(0x4a==cSrv2) ) {return 1;}//ENUM_MMS_FILE_CLOSE
//    }
//    
//    
//    
//    return 0;
//}

uint16 CXJPro103ClientWay::GetFrameNo()
{
    uint16 a = uFrameNo;
    uFrameNo++;
    return a;
}
//1:str1=str2  2:str1含str2  3:str2含str1
int CXJPro103ClientWay::isSubarray(const string &str1, const string &str2,int &pos)
{
    int nRet = -1;
    if (str1.size() == str2.size())
    {
        if (0 == str1.compare(str2))
        {
            nRet = 1;
        }
    }
    else if (str1.size() > str2.size())
    {
        pos = str1.find(str2);
        if (pos != string::npos)
        {
            nRet = 2;
        }
    }
    else if (str1.size() < str2.size())
    {
        pos = str2.find(str1);
        if (pos != string::npos)
        {
            nRet = 3;
        }
    }
    //int nRet = -1;
    //vector<BYTE> arr1,arr2;
    //arr1.insert(arr1.end(),str1.begin(),str1.end());
    //arr2.insert(arr2.end(),str2.begin(),str2.end());
    //
    //if (arr1.size() == arr2.size()) {
    //    if (equal(arr1.begin(), arr1.end(), arr2.begin())) {
    //        //printf("Array1 is equal Array2");
    //        return 1;
    //    }
    //}else if (arr1.size() > arr2.size()) {
    //    for (int i = 0; i < (arr1.size()-arr2.size()+1); i++) {
    //        if (equal(arr2.begin(), arr2.end(), arr1.begin() + i)) {
    //           //printf("Array2 is subarray of Array1");
    //            pos=i;
    //            nRet = 2;
    //            break;
    //        }
    //    }
    //}else{ //arr2.size()>arr1.size()
    //    for (int i = 0; i < (arr2.size()-arr1.size()+1); i++) {
    //        if (equal(arr1.begin(), arr1.end(), arr2.begin() + i)) {
    //            //printf("Array1 is subarray of Array2");
    //            pos=i;
    //            nRet = 3;
    //            break;
    //        }
    //    }
    //}
    m_rLogFile.FormatAdd(CLogFile::trace,"isSubarray ret[%d] pos[%d]", nRet, pos);
    return nRet;//不等
}
int CXJPro103ClientWay::SearchIedFromMMs(string &strMsg,bool &bFind,int &nFindCh)
{
    bool bFindIed=false;
    int nNum=0;//匹配到ied的数量
    string strFindIed;//最终iedname
    int nFindIedLen=0;//最终iedname长度
    int nFindIedLenCh=-1;//最终iedname通道
    
    int nMMsType = DecodeMMsType(strMsg);
    m_rLogFile.FormatAdd(CLogFile::trace, "[%s][CXJPro103ClientWay::SearchIedFromMMs] 通道号[%d] MMS报文类型[%d] ", m_strStnId.c_str(),
        nFindCh, nMMsType);
    if(1==nMMsType){
        //其他类型文件类的找不到ied走缓存
        // 不遍历设备ied，直接取当前文件类缓存通道号
        CLockUp lockUp(&m_LockFor860Step);
        map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(m_nFileTcNo);
        if(itNo!=m_map860IedList.end()){
            bFind = true;
            nFindCh = m_nFileTcNo;
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs] MMS 文件类报文非fileopen，走固定缓存的通道号[%d] msglen[%d] send to  stn!!!!!",m_strStnId.c_str(),
                    nFindCh,strMsg.length());
            return 1;
        }else{
            //找不到设备通道号，可能已关闭，不填充
            return -1;
        }
    }else{
        //2：file-open类严格找ied
        //非1和2: 非文件类,可能有录波列表
        
        //先遍历确认通道和iedname
        CLockUp lockUp(&m_LockFor860Step);
        for( map<int,STEP_INFO>::iterator itNo=m_map860IedList.begin();itNo!=m_map860IedList.end();itNo++ ){
            int nCh=-1;
            int nPosIed;
            if(3 == isSubarray(itNo->second.strIedName,strMsg,nPosIed)){
                bFindIed = true;
                nNum++;
                if(itNo->second.strIedName.length() > nFindIedLen){//pm2201A的包匹配pm2201成功，会发给pm2201
                    strFindIed = itNo->second.strIedName;
                    nFindIedLen = itNo->second.strIedName.length();
                    nFindIedLenCh = itNo->first;
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs()]MMS类型[%d] 长度[%d]找到ied[%s]ied-len[%d]通道[%d] 已找到[%d]个ied",m_strStnId.c_str(),
                            nMMsType,strMsg.length(),itNo->second.strIedName.c_str(),nFindIedLen,nFindIedLenCh,nNum);
                }
            }
        }
    }
    
    
    if(bFindIed){
        bFind = bFindIed;
        nFindCh = nFindIedLenCh;
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs()]MMS 最终确认ied[%s]len[%d]通道[%d] 共找到[%d]个ied",m_strStnId.c_str(),
                strFindIed.c_str(),nFindIedLen,nFindIedLenCh,nNum);
        
        //含?,需要替换msg字符 
        string strCom="?";
        int nPosNoUse;
        if(3 == isSubarray(strCom,strMsg,nPosNoUse)){
            char c[]={0x00};
            string strNull;
            strNull.assign(c,sizeof(c));
            string strKeyFind;
            strKeyFind = "?" + strFindIed;
            int nPosDel;
            if(3 == isSubarray(strKeyFind,strMsg,nPosDel)){//含 ?PL109                
                if(2 == nMMsType){//file-open类严格找ied //COMTRADE/xxxx_RCD_xxxx.cfg?PL109 文件类检查关键字[?IED] 缓存当前对象 
                    m_nFileTcNo = nFindIedLenCh;//招文件的头留存IED通道号
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs] MMS 文件类报文fileopen，MMS chkied[%s] cmp  msg has COMTRADE!!!!! msglen[%d]缓存 录波文件通道[%d]给其他文件类mms用",m_strStnId.c_str(),
                        strFindIed.c_str(),strMsg.length(),m_nFileTcNo);
                }else if(1 == nMMsType)
                {
                    //其他类型文件类的找不到ied走缓存 // 不遍历设备ied，直接取当前文件类缓存通道号 
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SearchIedFromMMs] MMS 一般文件类MMS报文，含?，不处理",m_strStnId.c_str());
                    return -11;//进不了，上面已return
                }else{
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs] MMS 非文件类报文 招录波列表处理，MMS chkied[%s] cmp  msg has COMTRADE!!!!! msglen[%d]",m_strStnId.c_str(),
                        strFindIed.c_str(),strMsg.length());
                }
                //COMTRADE/xxxx_RCD_xxxx.cfg?PL109 ==> COMTRADE/xxxx_RCD_xxxx.cfg?00000
                for(int i=0;i<strKeyFind.size();i++){
                    strMsg.replace(nPosDel+i,1,strNull);
                }
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SearchIedFromMMs] MMS ied[%s] 是COMTRADE替换[?IED]为null !!!!!替换后 msglen[%d],缓存文件类对象通道号[%d]",m_strStnId.c_str(),
                        strFindIed.c_str(),strMsg.length(),m_nFileTcNo);
                //PrintBytes(strMsg);
            }
        }
    }else{
        return -10;
    }
    
    return 0;
}

//asdu上来结果整理
int CXJPro103ClientWay::ResultHandleLoop()
{
	//m_rLogFile.FormatAdd(CLogFile::trace,"进入ResultHandleLoop");	

	bool bIsFailed(true);
	while (!IfExit()) 
	{
		MySleep(1);
		STTPCACHETYPE::iterator ite = m_STTPMsgCache.begin();
		STTPMSG_CACHE_TYPE* pSttpMsgCacheNode = NULL;
		while (ite != m_STTPMsgCache.end())  
                {
                    if (IfExit()) 
                    {
			break;
                    }
                    MySleep(1);

                    CLockUp localLock(&m_STTPCacheLock);
                    pSttpMsgCacheNode = *ite;
                    if (NULL == pSttpMsgCacheNode)
                    {
			ite = m_STTPMsgCache.erase(ite);
                            continue;
                    }
                    //printf("---------1-------");
                    if (IsSttpMsgGetFullResult(pSttpMsgCacheNode,bIsFailed)) 
                    {
               //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------IsSttpMsgGetFullResult full-------",m_strStnId.c_str());
			if (!bIsFailed) 
                        {
                            // m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------pSttpMsgCacheNode not Failed DistributeToOperation-------",m_strStnId.c_str());
                    //printf("---------3-------");
                            if (DistributeToOperationThread(pSttpMsgCacheNode)) 
                            {
                                ite = m_STTPMsgCache.erase(ite);
                                if(pSttpMsgCacheNode->uMsgId == 20015)
                                {
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 20015 不发20157-------",m_strStnId.c_str());
                                }
                                else if(pSttpMsgCacheNode->uMsgId == 20011)
                                {
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 20011 不发20157-------",m_strStnId.c_str());
                                }
                                else if(pSttpMsgCacheNode->uMsgId == 20007)
                                {
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 20007 不发20157-------",m_strStnId.c_str());
                                }
                                else if(pSttpMsgCacheNode->uMsgId == 40001)
                                {
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 40001 不发20157-------",m_strStnId.c_str());
                                }
                                else if(pSttpMsgCacheNode->uMsgId == 40003)
                                {
                                     m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 40003 不发20157-------",m_strStnId.c_str());
                                }
                                else if(pSttpMsgCacheNode->uMsgId == 203)
                                {
                                     m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 203 不发20157-------",m_strStnId.c_str());
                                }
                                else
                                {
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok 20157-------",m_strStnId.c_str());
                                    Publish20157(pSttpMsgCacheNode);
                                }
                            }
                            else
                            {
                               ++ite;
                               MySleep(1);  // 空闲处理业务完，等待处理 add by jjl 20141024
                            }
                        continue;
			}
                        else
                        {
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------pSttpMsgCacheNode IsFailed-------",m_strStnId.c_str());
                   
	                    // 回复失败前先判断设备的通信状态,若为正常，则失败号为“子站回复失败”，若为断开，则失败号为"设备断开";
                            int nReason = B_SUB_REPLY_FAILED-BUSINESS_ERROR_START_NO;
                            Respond20069ForCMD(pSttpMsgCacheNode,nReason);
                            Release103MsgCacheBySttpMsg(pSttpMsgCacheNode);
                            delete pSttpMsgCacheNode;
                            ite = m_STTPMsgCache.erase(ite);
					continue;
			}
		    }
                    else
                    {
                         //printf("[%s][CXJPro103ClientWay::ResultHandleLoop()]---------IsSttpMsgGetFullResult full false-------",m_strStnId.c_str());
                    }

                    UINT lsTimeOut = GetTimeOut(pSttpMsgCacheNode->uMsgId);
                    time_t timeNow;
                    time(&timeNow);
                    //超时回20069				
                    if ((timeNow - pSttpMsgCacheNode->tRecvTime) > lsTimeOut)
                    {
                        //真超时
                        //m_rLogFile.FormatAdd(CLogFile::trace,"---------4-------");
                        //未收到报文的时间大于超时时间视为超时
			if ((20042==pSttpMsgCacheNode->uMsgId)||(210==pSttpMsgCacheNode->uMsgId)||(20105==pSttpMsgCacheNode->uMsgId))
                        {
                            if (DistributeToOperationThread(pSttpMsgCacheNode))
                            {
                                ite = m_STTPMsgCache.erase(ite);//不用++，返回的就是next
                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------uMsgId[%d]tRecvTime fail 20157-lsTimeOut[%d] pSttpMsgCacheNode->tRecvTime[%d] timeNow[%d]------",m_strStnId.c_str(),
                                pSttpMsgCacheNode->uMsgId,lsTimeOut,pSttpMsgCacheNode->tRecvTime,timeNow);
                                Publish20157(pSttpMsgCacheNode);                        
                                continue;
                            }
			}
                        else
                        {
                            Respond20069ForCMD(pSttpMsgCacheNode,(B_SUB_REPLY_TIMEOUT-BUSINESS_ERROR_START_NO));
                            Release103MsgCacheBySttpMsg(pSttpMsgCacheNode);
                            delete pSttpMsgCacheNode;
                            ite = m_STTPMsgCache.erase(ite);
                            continue;
			}
                    }
                    else if ((timeNow - pSttpMsgCacheNode->tCMDTime) > lsTimeOut*0.1)
                    {
                           //防超时,防页面超时，每1/10时间叫页面继续等待
                           //m_rLogFile.FormatAdd(CLogFile::trace,"---------5-------");
                            //               m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------uMsgId[%d]tCMDTime fail 20157----[%d]tCMDTime[%d]----[%d]lsTimeOut[%f]------",m_strStnId.c_str(),
                            //                        pSttpMsgCacheNode->uMsgId,timeNow,pSttpMsgCacheNode->tCMDTime,lsTimeOut,lsTimeOut*0.1);
                            //                        time(&pSttpMsgCacheNode->tCMDTime);
                        if(pSttpMsgCacheNode->uMsgId == 20015)
                        {
                    
                        }
                        else if(pSttpMsgCacheNode->uMsgId == 20011)
                        {
                    
                        }
                        else if(pSttpMsgCacheNode->uMsgId == 20007)
                        {
                    
                        }
                        else if(pSttpMsgCacheNode->uMsgId == 40001)
                        {
                    
                        }
                        else if(pSttpMsgCacheNode->uMsgId == 40003)
                        {
                    
                        }
                        else if( (pSttpMsgCacheNode->uMsgId == 210) )
                        {
                    
                        }
                        else
                        {
                            //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ResultHandleLoop()]---------DistributeToOperationThread ok tCMDTime 发20157-------",m_strStnId.c_str());
                            Publish20157(pSttpMsgCacheNode);
                        }
                    }
                    ++ite;
		}
	}

	//m_rLogFile.FormatAdd(CLogFile::trace,"退出ResultHandleLoop");	
    
	return 0;
}
bool CXJPro103ClientWay::IsSttpMsgGetFullResult( STTPMSG_CACHE_TYPE* pSttpMsgCacheNode , bool& pIsFailed )
{
    //printf("---------IsSttpMsgGetFullResult-------");
	bool isFull = true;
	pSttpMsgCacheNode->iSize = 0;
	pIsFailed = true;
    ASDUMESSAGE_CACHE_TYPE* p103ASDUMsg = NULL;
	ASDUCACHETYPE::iterator Ite = pSttpMsgCacheNode->m103Cache.begin();
	while (Ite != pSttpMsgCacheNode->m103Cache.end()) {
        p103ASDUMsg= Ite->second;
        if (p103ASDUMsg != NULL) {
            //printf("---------IsSttpMsgGetFullResult------[%d]",p103ASDUMsg->bIsFull);
            isFull &= p103ASDUMsg->bIsFull;	
            if (p103ASDUMsg->tTimeFlag > pSttpMsgCacheNode->tRecvTime){
                pSttpMsgCacheNode->tRecvTime = p103ASDUMsg->tTimeFlag;
            }
            pSttpMsgCacheNode->iSize += p103ASDUMsg->iSize;
            pIsFailed &= p103ASDUMsg->bIsFailed;
        } 
		Ite++;
	}
    //printf("---------IsSttpMsgGetFullResult----isFull[%d]-pIsFailed[%d]--",isFull,pIsFailed);
	return isFull;	
}
void CXJPro103ClientWay::Release103MsgCacheBySttpMsg( STTPMSG_CACHE_TYPE* pSttpMsgCacheNode )
{
	int bRIIKey = 0;
    ASDUMESSAGE_CACHE_TYPE* p103Msg = NULL;
	ASDUCACHETYPE::iterator Ite = pSttpMsgCacheNode->m103Cache.begin();
	while (Ite != pSttpMsgCacheNode->m103Cache.end()) {
        p103Msg = Ite->second;
        if (p103Msg != NULL){
            bRIIKey = p103Msg->iIndexNo;
            if (-1 != bRIIKey)
            {
                m_index103Cache.DetachItem(bRIIKey);
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Release103MsgCacheBySttpMsg()]释放iKey[%d]",m_strStnId.c_str(),
                        bRIIKey);
            }
            delete p103Msg;
        }
        Ite++;
	}	
}
void CXJPro103ClientWay::Respond20069ForCMD( STTPMSG_CACHE_TYPE* pSttpMsgCacheNode ,int pResult )
{
    STTPMSG sttpResultMsg;
    bzero(&sttpResultMsg,sizeof(STTPMSG));
    CSttpMsgMaker pSttpMaker;
    pSttpMaker.Make20069Msg(sttpResultMsg,
                            pSttpMsgCacheNode->uRII,
                            pSttpMsgCacheNode->chPTId,
                            pSttpMsgCacheNode->iCpu,
                            pResult,
                            pSttpMsgCacheNode->uMsgId);
    
    STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}
void CXJPro103ClientWay::Publish20157( STTPMSG_CACHE_TYPE* pSttpMsgCacheNode )
{
    STTPMSG sttpResultMsg;
    bzero(&sttpResultMsg,sizeof(STTPMSG));
    CSttpMsgMaker pSttpMaker;
    pSttpMaker.Make20157Msg(sttpResultMsg,pSttpMsgCacheNode->uRII,
                            pSttpMsgCacheNode->chPTId,pSttpMsgCacheNode->iCpu,pSttpMsgCacheNode->iZone,
                            pSttpMsgCacheNode->uMsgId,pSttpMsgCacheNode->iSize);
    STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}
bool CXJPro103ClientWay::DistributeToOperationThread( STTPMSG_CACHE_TYPE* pSttpMsgCacheNode )
{
    bool bResult(true);
    {
        CLockUp lockUp(&m_LockForResultSttpList);
        m_ResultSttpList.push_back(pSttpMsgCacheNode);
    }
    return bResult;
}

//asdu上来结果发布池化
int CXJPro103ClientWay::LoadResultOperLoop()
{
    STTPMSG_CACHE_TYPE Sttp_Info;
    {
        CLockUp lockUp(&m_LockForResultSttpList);
        if(m_ResultSttpList.empty()){
            return 0;
        }
        Sttp_Info = *m_ResultSttpList.front();
        m_ResultSttpList.pop_front();
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::LoadResultOperLoop()]开始处理报文,队列中剩余报文[%d]..",m_strStnId.c_str(),m_ResultSttpList.size());
    }

    try{
        SttpResultHandle(&Sttp_Info);//deal one
    }
    catch(...) {
        m_rLogFile.FormatAdd(CLogFile::error,"命令处理发生异常");
    }
    return 0;
}
void CXJPro103ClientWay::SttpResultHandle(STTPMSG_CACHE_TYPE* m_pSttpCacheNode)
{
    STTP_MSG_QUEUE pSttpResultList;
    m_rASDUHandler.SttpResultHandle(m_pSttpCacheNode,pSttpResultList);
    STTP_MSG_QUEUE::iterator sttpIte = pSttpResultList.begin();
    while (sttpIte != pSttpResultList.end()){
        m_rLogFile.FormatAdd(CLogFile::trace,"----------[%s][CXJPro103ClientWay::SttpResultHandle]---m_pSttpCacheNode iMsgID [%d]",m_strStnId.c_str(),m_pSttpCacheNode->uMsgId);//20015
        if (20128 != m_pSttpCacheNode->uMsgId)
        {
            char chMsgID[6] = "";
            memcpy(chMsgID,sttpIte->msg.MsgHdr.m_byteMsgID,5);
            int iMsgID = atoi(chMsgID);
            m_rLogFile.FormatAdd(CLogFile::trace,"----------[%s][CXJPro103ClientWay::SttpResultHandle]---rcv iMsgID [%d]",m_strStnId.c_str(),iMsgID);//20016
            if ( (204==iMsgID) || (211==iMsgID) || (212==iMsgID) || (214==iMsgID) )
            {
                m_rLogFile.FormatAdd(CLogFile::trace,"--------1-[%s][CXJPro103ClientWay::PushAutoUpMsgToPublisher]",m_strStnId.c_str());
                PushAutoUpMsgToPublisher(sttpIte->msg);
                //先正常往外回复，然后内部转发一份自动招文件业务关注的报文
                if( 212==iMsgID )
                {
                    string strFileName;
                    {
                        STTP_FULL_DATA sttp_data;
                        zero_sttp_full_data(sttp_data);
                        CSttpMsgAnalyze pAnalyze2(&sttp_data);
                        pAnalyze2.Sttp_To_Data(&sttpIte->msg);
                        strFileName = sttp_data.sttp_body.variant_member.file_data.strFileName.c_str();
                    }
                   m_rLogFile.FormatAdd(CLogFile::trace,"----------[%s][CXJPro103ClientWay::SttpResultHandle]---rcv 212 [%s]",m_strStnId.c_str(),strFileName.c_str());
                    if(strstr(strFileName.c_str(),"/CONFIG/config.info")){
                        PushMsgToSTnInit(sttpIte->msg);
                    }
                    //if(strstr(strFileName.c_str(),"/MODEL/model.xml")){
					if(strstr(strFileName.c_str(),"/MODEL/")){
                        PushMsgToSTnInit(sttpIte->msg);
                    }
                    if(strstr(strFileName.c_str(),m_strScdPath.c_str())){
                        PushMsgToSTnInit(sttpIte->msg);
                    }
                }

				if( m_bStartCallModel && ( 204 == iMsgID ) && m_pSttpCacheNode->strFileName.find( "MODEL" ) )
				{
					m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::SttpResultHandle] 收到子站上送模型文件列表 sttp 204");
					GetModleFile(sttpIte->msg);
				}
			}else if(40002==iMsgID){
                PushMsgToSTnInit(sttpIte->msg);//透传的不走sttp往上，进业务管理
            }else if(40004==iMsgID){
                //PrintBytes(sttpIte->msg.MsgBody,0x2000);
                m_rLogFile.FormatAdd(CLogFile::trace,"--------40004-[%s][CXJPro103ClientWay::PushAutoUpMsgToPublisher] ",m_strStnId.c_str());
                PushAutoUpMsgToPublisher(sttpIte->msg);
            }else if(20012==iMsgID){
                //关闭通道
                {
                    STTP_FULL_DATA sttp_data;
                    zero_sttp_full_data(sttp_data);
                    CSttpMsgAnalyze pAnalyze2(&sttp_data);
                    pAnalyze2.Sttp_To_Data(&sttpIte->msg);
                    for(vector<STTP_DATA>::iterator it=sttp_data.sttp_body.variant_member.value_data.begin();it!=sttp_data.sttp_body.variant_member.value_data.end();it++){
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SttpResultHandle()]20012 ID[%d]状态",m_strStnId.c_str(),it->id);
                        int nDiId = it->id;
                        if( (nDiId>1001000) && (nDiId<1002000)){//组1通讯状态  1001016
                            int nChNo = nDiId - 1001000;
                            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SttpResultHandle()]20012 ID[%d]nChNo[%d]状态[%s]",m_strStnId.c_str(),it->id,nChNo,it->str_value.c_str());
                            if(0==strcmp(it->str_value.c_str(),"0")){//0-OFF  1- ON
                                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SttpResultHandle()]20012 ID[%d]nChNo[%d]状态[%s]0-OFF",m_strStnId.c_str(),it->id,nChNo,it->str_value.c_str());
                                CLockUp lockUp(&m_LockFor860Step);
                                map<int,STEP_INFO>::iterator itNo=m_map860IedList.find(nChNo);
                                if(itNo != m_map860IedList.end()){
                                    if( (itNo->second.nStep != LinkWtClose)&&(itNo->second.nStep != LinkWtConn)&&(itNo->second.nStep != WtReCC)&&(itNo->second.nStep != WtReInit)&&(itNo->second.nStep != LinkConned) ){
                                        itNo->second.nStep = LinkClosed;
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SttpResultHandle()]通道号[%d]状态 step[%d]退回LinkClosed nFailCount[%d]",m_strStnId.c_str(),nChNo,itNo->second.nStep,itNo->second.nFailCount);
                                    }else{
                                        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::SttpResultHandle()]通道号[%d]状态 step[%d]wait状态,暂不退回LinkClosed nFailCount[%d]",m_strStnId.c_str(),nChNo,itNo->second.nStep,itNo->second.nFailCount);
                                    }
//                                    itNo->second.tStepCur = time(NULL);
//                                    itNo->second.tStepNextTime = time(NULL);
                                    //itNo->second.nFailCount++;//不能++因为主站会不停召唤
                                    
                                }
                            }
                        }
                    }
                }
                PushAutoUpMsgToPublisher(sttpIte->msg);
            }else{
                m_rLogFile.FormatAdd(CLogFile::trace,"--------else-[%s][CXJPro103ClientWay::PushAutoUpMsgToPublisher]",m_strStnId.c_str());
				PushAutoUpMsgToPublisher(sttpIte->msg);
			}
        }else{
            PushAutoUpMsgToPublisher(sttpIte->msg);
        }
        ++sttpIte;
    }
    Release103MsgCacheBySttpMsg(m_pSttpCacheNode);
}
void CXJPro103ClientWay::PushAutoUpMsgToPublisher(STTPMSG &m_pSttpCacheNode)
{printf("----------[CXJPro103ClientWay::PushAutoUpMsgToPublisher]");
    STTP_FULL_DATA sttp_data;
    zero_sttp_full_data(sttp_data);
    CSttpMsgAnalyze pAnalyze2(&sttp_data);
    pAnalyze2.Sttp_To_Data(&m_pSttpCacheNode);

    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
   m_rLogFile.FormatAdd(CLogFile::trace,"----------[%s][CXJPro103ClientWay::PushAutoUpMsgToPublisher]插入一条uMsgID[%d]endFlag[%d]file_list.size[%d]sttp向上去页面的，m_SendBusList当前大小[%d]",
            m_strStnId.c_str(),sttp_data.sttp_head.uMsgID, sttp_data.sttp_head.uMsgEndFlag, sttp_data.sttp_body.variant_member.file_list.size(), m_SendBusList.size());
}
void CXJPro103ClientWay::PushMsgToSTnInit(STTPMSG &m_pSttpCacheNode)
{
    STTP_FULL_DATA sttp_data;
    zero_sttp_full_data(sttp_data);
    CSttpMsgAnalyze pAnalyze2(&sttp_data);
    pAnalyze2.Sttp_To_Data(&m_pSttpCacheNode);

    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForRcvHnTcSttpList);
        m_RcvHnTcSttpList.push_back(sSend);
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PushMsgToSTnInit]插入一条uMsgID[%d]sttp向透传业务线程的，m_RcvHnTcSttpList 当前大小[%d]",
             m_strStnId.c_str(),sttp_data.sttp_head.uMsgID,m_RcvHnTcSttpList.size());
    }
    
}


//commad-sttp下去
int CXJPro103ClientWay::CommandHandleLoop()
{
    STTP_FULL_DATA sttp_data;  
    {// 获取信息，为了自动释放锁加括号
        CLockUp lockUp(&m_LockForRcvBusInfList);//锁，出括号自己释放
        if(m_RcvBusInfList.empty()){
            return 0;
        }
        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
        Sttp_Info = m_RcvBusInfList.front();
        m_RcvBusInfList.pop_front();
        zero_sttp_full_data(sttp_data);
        sttp_data = Sttp_Info.sttp_data;
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]开始处理报文ID=%d,队列中剩余报文%d..",m_strStnId.c_str(),sttp_data.sttp_head.uMsgID,m_RcvBusInfList.size());
    }
    
    if(sttp_data.sttp_head.uMsgID==40001){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]40001装置通道编号[%d]规约类型[%d][1-860]命令类型[%d][0-con,1-close,2-data]包类型[%d][0-data,1-crc,3-item,6-cfg]数据包字节数[%d],Rii:%d",m_strStnId.c_str(),
                sttp_data.sttp_body.nCpu,sttp_data.sttp_body.nSource,sttp_data.sttp_body.nEventType,sttp_data.sttp_body.nCmdSource,sttp_data.sttp_body.nStatus,sttp_data.sttp_head.uMsgRii);
    }else 
    if(sttp_data.sttp_head.uMsgID==210){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]210子站ID[%s]文件名[%s]",m_strStnId.c_str(),
                sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.variant_member.file_data.strFileName.c_str());
        
        //界面210来的只有文件名，无路径，要识别添加映射路径给子站
        //自己生产的210带路径
        string strCmdFile = sttp_data.sttp_body.variant_member.file_data.strFileName.c_str();
        string strIp = sttp_data.sttp_body.variant_member.file_data.strReport.c_str();
        string strAddCmdFile;
        if(strstr(strCmdFile.c_str(),"/")){
            
        }else{
            {
                string strConfig=".info";
                int nPos = strCmdFile.find(strConfig);
                if( (nPos == (strCmdFile.length()-strConfig.length())) || (strIp.find("CONFIG") != std::string::npos) ){//末尾
                    strAddCmdFile = "/CONFIG/" + strCmdFile;
                    sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                }
            }
            bool bIsAutoScd=false;
            {
                int nPos = strCmdFile.find(m_strScdPath);
                if(  nPos == (strCmdFile.length()-m_strScdPath.length())  ){//末尾
                    bIsAutoScd = true;
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]210子站ID[%s]文件名[%s]识别为自动scd文件,bIsAutoScd[%d]，不根据文件名类型调整路径",m_strStnId.c_str(),
                        sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.variant_member.file_data.strFileName.c_str(),bIsAutoScd);
                }
            }
            if(!bIsAutoScd){//因为自动也可以填.scd结尾文件。其他因为定了所以可以统一处理页面和程序自动，scd没定名字只能分开处理
                string strScd=".scd";
                int nPos = strCmdFile.find(strScd);
                if((nPos == (strCmdFile.length()-strScd.length()) ) || (strIp.find("SCD") != std::string::npos)){//末尾
                    strAddCmdFile = "/SCD/" + strCmdFile;
                    sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                }
            }
            {
                string strSsd=".ssd";
                int nPos = strCmdFile.find(strSsd);
                if(  (nPos == (strCmdFile.length()-strSsd.length())) ||  (strIp.find("SSD") != std::string::npos) ){//末尾
                    strAddCmdFile = "/SSD/" + strCmdFile;
                    sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                }
            }
            {
                string strSvg=".svg";
                int nPos = strCmdFile.find(strSvg);
                if(  (nPos == (strCmdFile.length()-strSvg.length())) ||  (strIp.find("SVG") != std::string::npos) ){//末尾
                    strAddCmdFile = "/SVG/" + strCmdFile;
                    sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                }
            }
            {
                string strLog=".log";
                int nPos = strCmdFile.find(strLog);
                if( ( nPos == (strCmdFile.length()-strLog.length()))||  (strIp.find("LOG") != std::string::npos)  ){//末尾
                    strAddCmdFile = "/LOG/" + strCmdFile;
                    sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                }
            }
            {
                if(( (strstr(strCmdFile.c_str(),"model")) && (strstr(strCmdFile.c_str(),"Model")) ) ||  (strIp.find("MODEL") != std::string::npos) ){
                    if(strstr(strCmdFile.c_str(),".xml")){
                        strAddCmdFile = "/MODEL/" + strCmdFile;
                        sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
                    }
                }
            }
			{
				if( std::string::npos != strIp.find("PCAP") ){
					strAddCmdFile = "/PCAP/" + strCmdFile;
					sttp_data.sttp_body.variant_member.file_data.strFileName = strAddCmdFile.c_str();
				}
			}
        }
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]210子站ID[%s]调整后文件名[%s]",m_strStnId.c_str(),
                sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.variant_member.file_data.strFileName.c_str());
    }else 
    if(sttp_data.sttp_head.uMsgID==213){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()]213子站ID[%s]要下裝的文件名[%s]nOffset[%d]子站的IP[%s]rii[%d]",m_strStnId.c_str(),
                sttp_data.sttp_body.ch_pt_id,
                sttp_data.sttp_body.variant_member.file_data.strFileName.c_str(),
                sttp_data.sttp_body.variant_member.file_data.nOffset,
                sttp_data.sttp_body.variant_member.file_data.strReport.c_str(),
                sttp_data.sttp_body.variant_member.file_data.nFileNO);
    }else 
    if(sttp_data.sttp_head.uMsgID==20011){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()] 20011 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",m_strStnId.c_str(),
                    sttp_data.sttp_head.uMsgID,sttp_data.sttp_head.uMsgRii,
                    sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.nCpu,sttp_data.sttp_body.nSource,
                    sttp_data.sttp_body.nZone,sttp_data.sttp_body.nChangeConfigType);
    }else 
    if(sttp_data.sttp_head.uMsgID==20007){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()] 20007 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",m_strStnId.c_str(),
                    sttp_data.sttp_head.uMsgID,sttp_data.sttp_head.uMsgRii,
                    sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.nCpu,sttp_data.sttp_body.nSource,
                    sttp_data.sttp_body.nZone,sttp_data.sttp_body.nChangeConfigType);
    }else 
    if(sttp_data.sttp_head.uMsgID==20015){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()] 20015 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",m_strStnId.c_str(),
                    sttp_data.sttp_head.uMsgID,sttp_data.sttp_head.uMsgRii,
                    sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.nCpu,sttp_data.sttp_body.nSource,
                    sttp_data.sttp_body.nZone,sttp_data.sttp_body.nChangeConfigType);
    }else 
    if(sttp_data.sttp_head.uMsgID==20106){
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()] 20106 uMsgID[%d] rii[%d] pt[%s]cpu[%d]src[%d]zone[%d]nChangeConfigType[%d]",m_strStnId.c_str(),
                    sttp_data.sttp_head.uMsgID,sttp_data.sttp_head.uMsgRii,
                    sttp_data.sttp_body.ch_pt_id,sttp_data.sttp_body.nCpu,sttp_data.sttp_body.nSource,
                    sttp_data.sttp_body.nZone,sttp_data.sttp_body.nChangeConfigType);
    }else 
    if(sttp_data.sttp_head.uMsgID==203){
        //1-按文件名称 从0~当前时间
        if(sttp_data.sttp_body.nEventType==1){
            string strTime0,strTimeNow;
            strTime0 = "2000-01-01 00:00:00";
            {
                CXJTime CvtTime(time(0));
                strTimeNow = CvtTime.GetTimeString(CXJTime::STTP19Time).c_str();
            }
            snprintf(sttp_data.sttp_body.ch_time_20_BIT1,sizeof(sttp_data.sttp_body.ch_time_20_BIT1),"%s",strTime0.c_str());
            snprintf(sttp_data.sttp_body.ch_time_20_BIT2,sizeof(sttp_data.sttp_body.ch_time_20_BIT2),"%s",strTimeNow.c_str());
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommandHandleLoop()] 203 uMsgID[%d] rii[%d] pt[%s] file[%s] nEventType[%d]=1 改为查该时间范围start[%s] end[%s]",m_strStnId.c_str(),
                    sttp_data.sttp_head.uMsgID,sttp_data.sttp_head.uMsgRii,
                    sttp_data.sttp_body.ch_pt_id,
                    sttp_data.sttp_body.strFilenameWithPath.c_str(),
                    sttp_data.sttp_body.nEventType,
                    sttp_data.sttp_body.ch_time_20_BIT1,sttp_data.sttp_body.ch_time_20_BIT2);
        }
    }
    

    try{
        CommandHandle(sttp_data);//deal one
    }
    catch(...)
    {
        m_rLogFile.FormatAdd(CLogFile::error,"命令处理发生异常");
    }
    return 0;
}
void CXJPro103ClientWay::CommandHandle( const STTP_FULL_DATA &sttpCmdmsg )
{	
    UINT uMsgId = sttpCmdmsg.sttp_head.uMsgID;
    switch (uMsgId)
    {
    case 20015://定值--测试BH99999 cpu1 zone1处理 mdl值
    case 20011://开关量--测试BH99999 cpu1 处理  通道状态，子站状态grp=1，,2
    case 20007:	   //模拟量。desc="子站测量信息"--测试BH99999 grp=3
        
    case 203:	   // 通用文件列表下载要求
    case 210:	   // 通用文件下载要求
    case 40001:	   //透传
    case 40003:	   //配置生效命令
        try {
            CommonCMDOperation(sttpCmdmsg);	
        }
        catch(...){
            m_rLogFile.FormatAdd(CLogFile::error,"::CommandHandle] CommonCMDOperation函数发生异常（msgID=%d）",uMsgId);
        }
        break;
    case 213: //文件下装
        try {
            CommonFileDownLoadOperation(sttpCmdmsg);	
        }catch(...){
            m_rLogFile.FormatAdd(CLogFile::error,"::CommandHandle] CommonFileDownLoadOperation 函数发生异常（msgID=%d）",uMsgId);
        }
        break;
    default:
        m_rLogFile.FormatAdd(CLogFile::error,"::CommandHandle] 从发布者对象获得无效的命令报文（msgID=%d）,丢弃",uMsgId);
        break;
    }
}
void CXJPro103ClientWay::CommonCMDOperation( const STTP_FULL_DATA& pSttpCmdMsg )
{
    if (m_bIniting) {//正在初始化，命令直接回失败
        RefuseOperation(pSttpCmdMsg,(B_SUB_INITIALIZING-BUSINESS_ERROR_START_NO));
    }else if (!m_bConnected){//连接中断，命令直接回失败
        RefuseOperation(pSttpCmdMsg,(B_SUB_DISCONTINUE-BUSINESS_ERROR_START_NO));
    }else{
        //非初始化状态，正常命令流程
        STTPMSG_CACHE_TYPE* pSttpMsgCacheNode = new STTPMSG_CACHE_TYPE;
        if (NULL == pSttpMsgCacheNode)
        {
            RefuseOperation(pSttpCmdMsg,(B_SUB_INVALID_COMMAND-BUSINESS_ERROR_START_NO));
			m_rLogFile.FormatAdd(CLogFile::trace,"命令处理 new STTPMSG_CACHE_TYPE 失败，回复20069");
        }
        ASDUCACHETYPE& pASDUCacheList = pSttpMsgCacheNode->m103Cache;
        pASDUCacheList.clear();
        int iRet = m_rASDUHandler.SttpCommandHandle(pSttpCmdMsg,pASDUCacheList);//sttp转asdu
        if (1 == iRet) {//转asdu成功
            if (0 == pASDUCacheList.size()) {
                iRet = 0;
                m_rLogFile.FormatAdd(CLogFile::trace,"生成103命令0条");
            }else{
                int iKey(-1);
                int iAdd(-1);
                int iTempCpu(-1);

                pSttpMsgCacheNode->uMsgId= pSttpCmdMsg.sttp_head.uMsgID;
                pSttpMsgCacheNode->uRII = pSttpCmdMsg.sttp_head.uMsgRii;
                //取observerID
                bzero(pSttpMsgCacheNode->chObserverId,13);
                //取PTID
                bzero(pSttpMsgCacheNode->chPTId,13);
                memcpy(pSttpMsgCacheNode->chPTId,pSttpCmdMsg.sttp_body.ch_pt_id,12);
                //
                pSttpMsgCacheNode->iCpu = pSttpCmdMsg.sttp_body.nCpu;
                pSttpMsgCacheNode->iZone = pSttpCmdMsg.sttp_body.nZone;
                pSttpMsgCacheNode->iSource = pSttpCmdMsg.sttp_body.nSource;
				if ( 203 == pSttpCmdMsg.sttp_head.uMsgID )
				{
					pSttpMsgCacheNode->strFileName = pSttpCmdMsg.sttp_body.strFilenameWithPath;
				}
				else
				{
					pSttpMsgCacheNode->strFileName = pSttpCmdMsg.sttp_body.variant_member.file_data.strFileName;
				}
				pSttpMsgCacheNode->strFileType = pSttpCmdMsg.sttp_body.variant_member.file_data.strReport;
                
                bool bSendOk=true;
                //asdu的cache
                ASDUMESSAGE_CACHE_TYPE* pASDUCache = NULL;
                ASDUCACHETYPE::iterator ite = pASDUCacheList.begin();
                while (ite != pASDUCacheList.end()) {
                    iKey = ite->first;
                    pASDUCache = ite->second;
                    iAdd = pASDUCache->out103Msg.iAddr;
                    iTempCpu = pASDUCache->out103Msg.iCpu;

                    if (!IfExit()){
                        //103命令放入缓存操作
                        int ret = Send103ASDUCommand(iKey, pASDUCache);
                        if(ret!=0){
                            m_rLogFile.FormatAdd(CLogFile::error,"将103命令放入缓存时发生异常,addr:%d,cpu:%d",iAdd,iTempCpu);
                            bSendOk = false;
                        }
                    }else{
                        break;
                    }
                    ++ite;
                }
                
                if(bSendOk){
                    //放入sttp队列的cache
                    bool bCacheSttp=true;
                    if(pSttpCmdMsg.sttp_head.uMsgID==40001){
                        if (pSttpCmdMsg.sttp_body.nEventType==2){//CMDTYP<2>:=透传数据包
                            if(pSttpCmdMsg.sttp_body.nCmdSource==0){//0	透传报文 没法对应所以不cache
                                bCacheSttp = false;
                            }
                            if(pSttpCmdMsg.sttp_body.nCmdSource==5){//包类型 5 子站上送：信息条目值
                                bCacheSttp = false;
                            }
                            if(pSttpCmdMsg.sttp_body.nCmdSource==8){//包类型 8 子站上送：配置变化
                                bCacheSttp = false;
                            }
                        }
                         if (pSttpCmdMsg.sttp_body.nEventType==3 || pSttpCmdMsg.sttp_body.nEventType==4){//抓包数据包
                              bCacheSttp = true;
                         }
                    }

                    if(bCacheSttp){
                        time(&pSttpMsgCacheNode->tCMDTime);
                        time(&pSttpMsgCacheNode->tRecvTime);
                        CLockUp localLock(&m_STTPCacheLock);
                        m_STTPMsgCache.push_back(pSttpMsgCacheNode);
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation]sttp CMDTYP<%d> 包类型%d 走主动上送的透传包 删除cache",m_strStnId.c_str(),pSttpCmdMsg.sttp_body.nEventType,pSttpCmdMsg.sttp_body.nCmdSource);
                        ClearASDUCacheList(pASDUCacheList);
                        delete pSttpMsgCacheNode;
                    }
                }else{
                    if(pSttpCmdMsg.sttp_head.uMsgID==40001){
                        if (pSttpCmdMsg.sttp_body.nEventType==2){//CMDTYP<2>:=透传数据包
                            if(pSttpCmdMsg.sttp_body.nCmdSource==0){//0:透传报文
                                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation]sttp id[%d] 转asdu发送时有失败bSendOk[%d]，不cache,透传包回复假error防止卡主",m_strStnId.c_str(),
                                        pSttpCmdMsg.sttp_head.uMsgID,bSendOk);
                                string strMsg;
                                strMsg.assign(pSttpCmdMsg.sttp_body.strMessage);
                                int r = SendError2Front(pSttpCmdMsg.sttp_body.nCpu,strMsg);
                                if(r == 0){
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation()]回复40001-2-0透传长度[%d]包的失败包，回复成功， 发送通道[%d]",m_strStnId.c_str(),
                                            strMsg.size(),pSttpCmdMsg.sttp_body.nCpu);
                                }else{
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation()]回复40001-2-0透传长度[%d]包的失败包，回复失败， 发送通道[%d]",m_strStnId.c_str(),
                                            strMsg.size(),pSttpCmdMsg.sttp_body.nCpu);
                                }
                            }
                        }
                        if (pSttpCmdMsg.sttp_body.nEventType==3 || pSttpCmdMsg.sttp_body.nEventType==4){
                             string strMsg;
                             strMsg.assign(pSttpCmdMsg.sttp_body.strMessage);
                             int r = SendError2Front(pSttpCmdMsg.sttp_body.nCpu,strMsg);
                              if(r == 0){
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation()]回复40001-2-0透传长度[%d]包的失败包，回复成功， 发送通道[%d]",m_strStnId.c_str(),
                                            strMsg.size(),pSttpCmdMsg.sttp_body.nCpu);
                                }else{
                                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation()]回复40001-2-0透传长度[%d]包的失败包，回复失败， 发送通道[%d]",m_strStnId.c_str(),
                                            strMsg.size(),pSttpCmdMsg.sttp_body.nCpu);
                                }
                        }
                    }
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation]sttp id[%d]rii[%d] 转asdu发送时有失败bSendOk[%d]，删除cache",m_strStnId.c_str(),
                            pSttpCmdMsg.sttp_head.uMsgID,pSttpCmdMsg.sttp_head.uMsgRii,bSendOk);
                    ClearASDUCacheList(pASDUCacheList);
                    delete pSttpMsgCacheNode;
                }
            }
		}else if (0 == iRet){
			//NoSupportedOperation(pSttpCmdMsg);
			m_rLogFile.FormatAdd(CLogFile::trace,"命令不支持，回复20125,删除cache");
            ClearASDUCacheList(pASDUCacheList);
            delete pSttpMsgCacheNode;
		}else{
			RefuseOperation(pSttpCmdMsg,(B_SUB_INVALID_COMMAND-BUSINESS_ERROR_START_NO));
			m_rLogFile.FormatAdd(CLogFile::trace,"命令处理失败，回复20069");
            ClearASDUCacheList(pASDUCacheList);
            delete pSttpMsgCacheNode;
		}
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::CommonCMDOperation()]  pASDUCacheList[%d]",m_strStnId.c_str(),pASDUCacheList.size());
	}
}
void CXJPro103ClientWay::RefuseOperation( const STTP_FULL_DATA& pSttpCmdMsg , int pResult )
{
    STTP_FULL_DATA sSttp=pSttpCmdMsg;
    CSttpMsgAnalyze pAnalyze(&sSttp);
    STTPMSG_QUEUE  vMsg;
    pAnalyze.Data_To_Sttp(vMsg);
    if(vMsg.size()!=1){
        m_rLogFile.FormatAdd(CLogFile::error,"%s，回复20069 Data_To_Sttp err",getErrorMsg(BUSINESS_ERROR_START_NO+pResult));
        return ;
    }
    STTPMSG SourceMsg=vMsg.front();
    
    STTPMSG sttpResultMsg;
    bzero(&sttpResultMsg,sizeof(STTPMSG));
    CSttpMsgMaker pSttpMaker;
    pSttpMaker.Make20069Msg(sttpResultMsg,SourceMsg,pResult);
    
    
	STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
    
    m_rLogFile.FormatAdd(CLogFile::error,"%s，回复20069",getErrorMsg(BUSINESS_ERROR_START_NO+pResult));
}
int CXJPro103ClientWay::Send103ASDUCommand( int iKey, ASDUMESSAGE_CACHE_TYPE* pASDUCache )
{
    //printf("[%s][CXJPro103ClientWay::Send103ASDUCommand]iTYP[%d]iKey[%d]",m_strStnId.c_str(),(unsigned char)pASDUCache->out103Msg.iTYP,iKey);
    int nCount = m_index103Cache.CountItems(iKey);//TODO 内存泄露点，非cache可以不缓存key
    bool bSend = SendASDUMsgToAPCI(pASDUCache->out103Msg);
    
    if (  (0 == nCount) && (bSend)   )
    {   //上送类不会下去所以不会
        if( (pASDUCache->out103Msg.iTYP==200)&&(pASDUCache->out103Msg.INFSET[14]==0)&&(pASDUCache->out103Msg.INFSET[9]==2) )
        {
            //200的201-0data无法rii对应，所以不cache走上送玩法
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Send103ASDUCommand]透传报文包 iTYP[%d]iKey[%d] asdu200 CMDTYP<2>包类型0 透传包不cache",m_strStnId.c_str(),(unsigned char)pASDUCache->out103Msg.iTYP,iKey);
        }
        else
        {
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::Send103ASDUCommand]非透传报文 iTYP[%d]CMDTYP[%d]包类型[%d]  iKey[%d] cache wait rep",m_strStnId.c_str(),
                    (unsigned char)pASDUCache->out103Msg.iTYP,pASDUCache->out103Msg.INFSET[9],   pASDUCache->out103Msg.INFSET[14], iKey);
            
            time(&pASDUCache->tTimeFlag);
            m_index103Cache.AttachItem(iKey,pASDUCache);
            pASDUCache->iIndexNo = iKey;
        }
    }
    else
    {
       m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::Send103ASDUCommand]iTYP[%d]iKey[%d] send err,failed. nCount[%d]bSend[%d]",m_strStnId.c_str(),
               (unsigned char)pASDUCache->out103Msg.iTYP,iKey,nCount,bSend);
        pASDUCache->bIsFailed = true;
        pASDUCache->bIsFull = true;
        return -1;
    }
    
    return 0;
}
bool CXJPro103ClientWay::SendASDUMsgToAPCI( ASDUMESSAGE& pASDUMsg )
{
    if (NULL != &m_rAPCIHandler){
        return m_rAPCIHandler.PostASDUMsg(pASDUMsg);
    }else{
         m_rLogFile.FormatAdd(CLogFile::error,"APCIHandler 指针为空");
         return false;
    }
}
void CXJPro103ClientWay::CommonFileDownLoadOperation( const STTP_FULL_DATA& pSttpCmdMsg )
{
    if (m_bIniting)
    {//正在初始化，命令直接回失败
        RefuseOperation(pSttpCmdMsg,(B_SUB_INITIALIZING-BUSINESS_ERROR_START_NO));
    }
    else if (!m_bConnected)
    {//连接中断，命令直接回失败
        RefuseOperation(pSttpCmdMsg,(B_SUB_DISCONTINUE-BUSINESS_ERROR_START_NO));
    } 
    else
    {//分发给线程池中的线程处理
        if (!DistributeToOperationThread(pSttpCmdMsg)){
                RefuseOperation(pSttpCmdMsg,(B_SUB_DATAHANDLE_FAILED-BUSINESS_ERROR_START_NO));
                m_rLogFile.FormatAdd(CLogFile::error,"CFDownloadOperation对象分配操作线程失败");
        }
	}	
}
bool CXJPro103ClientWay::DistributeToOperationThread( const STTP_FULL_DATA& pSttpCmdMsg )
{
    bool bResult(true);
    {
        BUS_RECV_STTPFULLDATA_INFO s1;
        s1.sttp_data = pSttpCmdMsg;
        s1.t_time = time(NULL);
        CLockUp lockUp(&m_LockForOperationSttpList);
        m_OperationSttpList.push_back(s1);
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::DistributeToOperationThread()]加入队列，处理报文ID=%d,队列中剩余报文%d..",m_strStnId.c_str(),s1.sttp_data.sttp_head.uMsgID,m_OperationSttpList.size());
    }
    return bResult;
}


int  CXJPro103ClientWay::sttp2MMs40005(string strIedName, string strRef, int &nChNo,string &strErrMsg)
{
    if(m_nUseMMS!=1){
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::sttp2MMs40005()] 未开启解析库开关，不处理40005",m_strStnId.c_str());
        strErrMsg="未开启解析库开关";
        return -13;
    }
    
    map<string,int>::iterator itChNo = m_mapIedName2ChNo.find(strIedName);
    if(itChNo != m_mapIedName2ChNo.end()){
        {
            CLockUp lockUp1(&m_LockForCrc40005SttpCache);
            map<int,RCV_40005_INFO >::iterator itSave = m_mapCrc40005SttpCache.find(itChNo->second);
            if(itSave != m_mapCrc40005SttpCache.end()){//有缓存
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::sttp2MMs40005()] 参引[%s]失败,通道[%d]doing[%d]已有一个sttp在召唤 rii[%d]",m_strStnId.c_str(),
                        strRef.c_str(),itChNo->second,itSave->second.bIsDoing,
                        itSave->second.s40005Sttp.sttp_data.sttp_head.uMsgRii);
                strErrMsg="该设备有sttp在处理";
                return -12;
            }
        }
        
        //准备mms包
        DomainSpecific sDomain;
        std::size_t pos = strRef.find("/");  // PL2201PORT/LDHD1$DC$PhyNam$swRev
        if(pos!=std::string::npos){
            sDomain.DomainID = strRef.substr(0,pos);//PL2201PORT
            sDomain.ItemID = strRef.substr(pos+1);//LDHD1$DC$PhyNam$swRev
        }

        if(sDomain.DomainID.empty()||sDomain.ItemID.empty()){
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::sttp2MMs40005()] 参引[%s] strDomainId[%s] strItem[%s] 失败",m_strStnId.c_str(),strRef.c_str(),sDomain.DomainID.c_str(),sDomain.ItemID.c_str());
            strErrMsg="参引拆分异常";
            return -1;
        }else{
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::sttp2MMs40005()] 参引[%s] strDomainId[%s] strItem[%s] 成功",m_strStnId.c_str(),strRef.c_str(),sDomain.DomainID.c_str(),sDomain.ItemID.c_str());
        }
        List_Domain listDomain;
        listDomain.push_back(sDomain);

        string strReadMMsTpkt;
        m_pCMMSAnalyze->Pack_Read(CrcVerMMsInvokeId,listDomain,strReadMMsTpkt);

        if(strReadMMsTpkt.empty()){
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::sttp2MMs40005()]  参引[%s] Pack_Read()失败 ",m_strStnId.c_str(),strRef.c_str());
            strErrMsg="组装mms报文异常";
            return -2;
        }
        
        //发送mms
        {
            CLockUp lockUp1(&m_LockForRcvHnTcMsgChNoList);
            map<int,RCV_TC_INFO>::iterator itCh=m_mapRcvHnTcMsg.find(itChNo->second);
            if(itCh!=m_mapRcvHnTcMsg.end()){
                itCh->second.RcvHnTcMsgList.push_back(strReadMMsTpkt);
                PrintBytes(strReadMMsTpkt);
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::sttp2MMs40005()]MMS 通道号[%d]有MMS读版本信息的包 短包 剩余 该设备待透传msg[%d]条  ",m_strStnId.c_str(),
                    itCh->first,itCh->second.RcvHnTcMsgList.size());
                nChNo = itChNo->second;
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::sttp2MMs40005()]MMS 通道号[%d]有MMS读版本信息的包 短包 没找到ied通道无法透传，剩余 该设备待透传msg[%d]条 ",m_strStnId.c_str(),
                    itCh->first,itCh->second.RcvHnTcMsgList.size());
                strErrMsg="找不到通道发送";
                return -11;
            }
        }
    }else{
        m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::sttp2MMs40005()]MMS 报文的iedname[%s] 找不到 通道号",m_strStnId.c_str(),strIedName.c_str());
        strErrMsg="iedname找不到通道号";
        return -10;
    }
    
    
    return 0;
}


//突发上送
int CXJPro103ClientWay::AutoUpHandleLoop()
{
	//m_rLogFile.FormatAdd(CLogFile::trace,"进入AutoUpHandleLoop");

	ASDUMESSAGE ASDUMessage;

	while (!IfExit()) {
		MySleep(1);

		if (m_AutoUpASDUBuf.empty()) {
			continue;
		}
		
		{
			CLockUp localLock(&m_AutoUpASDULock);
			//取一条自动上送的ASDU
			ASDUMessage = m_AutoUpASDUBuf.front();
			m_AutoUpASDUBuf.pop_front();
		}

		STTP_MSG_QUEUE pSttpResultList;
        //printf("--------1------");
		m_rASDUHandler.AutoupASDUHandle(ASDUMessage,pSttpResultList);
        //printf("--------2------");
		STTP_MSG_QUEUE::iterator sttpIte = pSttpResultList.begin();
		while (sttpIte != pSttpResultList.end())
		{
            STTPMSG sttpResultMsg;
            bzero(&sttpResultMsg,sizeof(STTPMSG));
            sttpResultMsg = sttpIte->msg;
            STTP_FULL_DATA sttp_data;
            zero_sttp_full_data(sttp_data);
            CSttpMsgAnalyze pAnalyze2(&sttp_data);
            pAnalyze2.Sttp_To_Data(&sttpResultMsg);

            BUS_RECV_STTPFULLDATA_INFO sSend;
            sSend.sttp_data = sttp_data;
            sSend.t_time = time(NULL);
            
            if(sttp_data.sttp_head.uMsgID == 40002){
                if(sttp_data.sttp_body.nEventType==CmdProxyPacket){
                    if(sttp_data.sttp_body.nCmdSource==ParketRepItem){
                        //4	子站回复：信息条目值
                        //和流程相关，不放这里，Read40002ItemUp(sttp_data);
                        PushToSTnFlow(sttp_data);
                    }else 
                    if(sttp_data.sttp_body.nCmdSource==ParketAutoUpItem){//子站上送：信息条目值
                        //5	子站上送：信息条目值
                        //和流程相关，不放这里，Read40002ItemUp(sttp_data);
                        PushToSTnFlow(sttp_data);
                    }else
                    if (sttp_data.sttp_body.nCmdSource==ParketProxy){//透传报文 没法对应所以不cache
                        PushToSTnFlow(sttp_data);
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::AutoUpHandleLoop()]msgid[40002] CmdSource[%d]，result type:%d ",m_strStnId.c_str(),sttp_data.sttp_body.nCmdSource,sttp_data.sttp_body.nChangeInfoType);
                    }else
                    if (sttp_data.sttp_body.nCmdSource==ParketAutoUpCfgChg){//透传报文 主动上送配置变化
                        PushToSTnFlow(sttp_data);
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::AutoUpHandleLoop()]msgid[40002] CmdSource[%d] ",m_strStnId.c_str(),sttp_data.sttp_body.nCmdSource);
                    }else 
                    if (sttp_data.sttp_body.nCmdSource==ParketAutoUpScdChg){//透传报文 子站上送：SCD变化
                        PushToSTnFlow(sttp_data);
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::AutoUpHandleLoop()]SCD msgid[40002] CmdSource[%d] ",m_strStnId.c_str(),sttp_data.sttp_body.nCmdSource);
                    }else{
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::AutoUpHandleLoop()]msgid[%d] 异常的CmdSource[%d][4,5,0,8,9外] ",m_strStnId.c_str(),sttp_data.sttp_head.uMsgID, sttp_data.sttp_body.nCmdSource);
                    }
                }
            }else{
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::AutoUpHandleLoop()]msgid[%d] 直接走sttp出去 ",m_strStnId.c_str(),sttp_data.sttp_head.uMsgID);
                CLockUp lockUp(&m_LockForSendBusList);
                m_SendBusList.push_back(sSend);
            }
            
			++sttpIte;
		}
	}

	//m_rLogFile.FormatAdd(CLogFile::trace,"退出AutoUpHandleLoop");
	return 0;
}
void CXJPro103ClientWay::PushToSTnFlow(const STTP_FULL_DATA &m_pSttp)
{
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = m_pSttp;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForRcvHnTcSttpList);
        m_RcvHnTcSttpList.push_back(sSend);
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PushToSTnFlow()]msgid[%d] CmdSource[%d] m_RcvHnTcSttpList[%d] ,result type%d",m_strStnId.c_str(),
                m_pSttp.sttp_head.uMsgID, m_pSttp.sttp_body.nCmdSource, m_RcvHnTcSttpList.size(),m_pSttp.sttp_body.nChangeInfoType);
    }
   }

//下装
int CXJPro103ClientWay::LoadOperationLoop(THREAD_ID MyThreadId)
{
    STTP_FULL_DATA sttp_data;  
    {
        //池化的多线程同时管list的，判空和取值要一起锁住，不然其他线程会一起判空后pop会崩
        CLockUp lockUp(&m_LockForOperationSttpList);//锁，出括号自己释放
        if(m_OperationSttpList.empty()){
            return 0;
        }else{
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::LoadOperationLoop()]开始处理报文,队列中报文[%d]",m_strStnId.c_str(),m_OperationSttpList.size());
        }
        zero_sttp_full_data(sttp_data);
        BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
        Sttp_Info = m_OperationSttpList.front();
        m_OperationSttpList.pop_front();
        sttp_data = Sttp_Info.sttp_data;
    }
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::LoadOperationLoop()]开始处理报文ID=[%d],队列中剩余报文[%d]..",m_strStnId.c_str(),sttp_data.sttp_head.uMsgID,m_OperationSttpList.size());

    try{
        OperationHandle(sttp_data,MyThreadId);//deal one
    }
    catch(...) {
        m_rLogFile.FormatAdd(CLogFile::error,"LoadOperationLoop命令处理发生异常");
    }
    return 0;
}
int CXJPro103ClientWay::OperationHandle(const STTP_FULL_DATA& m_SttpCmdMsg,THREAD_ID MyThreadId)
{
    if (RequestDownload(m_SttpCmdMsg,MyThreadId)) {
        if (ContentDownLoad(m_SttpCmdMsg,MyThreadId)) {
            m_rLogFile.FormatAdd(CLogFile::trace,"文件内容下装成功");
        }
        else{
            m_rLogFile.FormatAdd(CLogFile::error,"文件内容下装时发生错误");
        }
    } else{
        m_rLogFile.FormatAdd(CLogFile::error,"请求文件下装失败");
    }
    
    return 0;
}
bool CXJPro103ClientWay::RequestDownload(const STTP_FULL_DATA& pSttpData,THREAD_ID MyThreadId)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::RequestDownload()] ",m_strStnId.c_str());
    bool bResult(false);
    ASDUCACHETYPE pASDUCacheList;
    pASDUCacheList.clear();
    int iRet = m_rASDUHandler.SttpCommandHandle(pSttpData,pASDUCacheList);
              
    if (1 == iRet) {
        if (0 == pASDUCacheList.size()) {
            iRet = 0;
            m_rLogFile.FormatAdd(CLogFile::trace,"生成103命令0条");
        }else{
            ASDUMESSAGE_CACHE_TYPE* pASDUCache = NULL;
            int iKey(-1);
            char chLog[255]="";
            ASDUCACHETYPE::iterator ite = pASDUCacheList.begin();
            if (ite != pASDUCacheList.end()) {
               m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::RequestDownload()] send pASDUCache",m_strStnId.c_str());
                iKey = ite->first;
                pASDUCache = ite->second;
                Send103ASDUCommand(iKey,pASDUCache);
                if(IfGetResult(GetTimeOut(210),pASDUCache,chLog)){//没有213借用210的
                    ASDULIST::iterator inIte = pASDUCache->in103MsgVector.begin();
                    if (inIte != pASDUCache->in103MsgVector.end()) {
                        STTP_FULL_DATA resultSttpData;
                        zero_sttp_full_data(resultSttpData);
                        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::RequestDownload()] IntermediateResultHandle before ",m_strStnId.c_str());
                        if (1 == m_rASDUHandler.IntermediateResultHandle(*inIte,resultSttpData)) {//发和回的asdu
                            if ((214==resultSttpData.sttp_head.uMsgID) && (1 ==resultSttpData.sttp_body.variant_member.file_data.nOffset)){
                                map<THREAD_ID,THREAD_INFO> ::iterator itWindow = m_mapOperThreadWindowInfo.find(MyThreadId);
                                if(itWindow != m_mapOperThreadWindowInfo.end()){
                                    itWindow->second.m_iSlideWindows = (resultSttpData.sttp_body.variant_member.file_data.nFileNO>0)? resultSttpData.sttp_body.variant_member.file_data.nFileNO : 1;
                                    bResult = true;
                                }else{
                                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::RequestDownload()] IntermediateResultHandle MyThreadId[%d] not find ",m_strStnId.c_str(),MyThreadId);
                                }
                            }
                        }
                    }
                }
                if (!bResult) {
                    PublishOperationResult(0,0,pSttpData);
                    m_rLogFile.FormatAdd(CLogFile::error,"文件下装请求失败");
                } 
            }  
        }
    } else if (0 == iRet) {
        NoSupportedOperation(pSttpData);
        m_rLogFile.FormatAdd(CLogFile::error,"文件下装请求命令不支持，回复20125");
    } else {
        PublishOperationResult(0,0,pSttpData);
        m_rLogFile.FormatAdd(CLogFile::error,"文件下装请求命令处理失败");
    }
    ClearASDUCacheList(pASDUCacheList);
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::RequestDownload()] ClearASDUCacheListh()pASDUCacheList[%d]",m_strStnId.c_str(),pASDUCacheList.size());
    return bResult;
}
bool CXJPro103ClientWay::ContentDownLoad(const STTP_FULL_DATA& pSttpData,THREAD_ID MyThreadId )
{m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] ",m_strStnId.c_str());
    bool bResult(false);
    ASDUCACHETYPE pASDUCacheList;
    pASDUCacheList.clear();
    
    STTP_FULL_DATA s2=pSttpData;
    s2.sttp_head.uMsgID = 214;
    int iRet = m_rASDUHandler.SttpCommandHandle(s2,pASDUCacheList);
    
    
    int m_iSlideWindows;
    map<THREAD_ID,THREAD_INFO> ::iterator itWindow = m_mapOperThreadWindowInfo.find(MyThreadId);
    if(itWindow != m_mapOperThreadWindowInfo.end()){
        m_iSlideWindows = itWindow->second.m_iSlideWindows;
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad]协商后窗口尺寸m_iSlideWindows[%d]",m_strStnId.c_str(),m_iSlideWindows);
    }else{
       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()]  MyThreadId[%d] not find ",m_strStnId.c_str(),MyThreadId);
    }
    if (1 == iRet){
        if (0 == pASDUCacheList.size()) {
            iRet = 0;
            m_rLogFile.FormatAdd(CLogFile::trace,"生成103命令0条");
        }else{
            ASDUMESSAGE_CACHE_TYPE* pASDUCache = NULL;
            int iKey(-1);
            int iCount(0);
            int iSentSize(0);
            char chLog[255]="";
            ASDUCACHETYPE::iterator ite = pASDUCacheList.begin();
            while(ite != pASDUCacheList.end()) {//转换好的asdulist-多帧
                iKey = ite->first;
                pASDUCache = ite->second;
                //发每一帧
                //printf("[%s][CXJPro103ClientWay::ContentDownLoad] 发每一帧 pASDUCache->bIsFull[%d] now reset false",m_strStnId.c_str(),pASDUCache->bIsFull);
                //pASDUCache->bIsFull = false;
                //printf("[%s][CXJPro103ClientWay::ContentDownLoad] 发每一帧 pASDUCache->bIsFull[%d] now",m_strStnId.c_str(),pASDUCache->bIsFull);
                if (SendASDUMsgToAPCI(pASDUCache->out103Msg)){
                   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad] SendASDUMsgToAPCI ok[%d]",m_strStnId.c_str(),iKey);
                    if (0 == m_index103Cache.CountItems(iKey)){
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad] m_index103Cache.CountItems key[%d] find 0  ",m_strStnId.c_str(),iKey);
                        time(&pASDUCache->tTimeFlag);
                        m_index103Cache.AttachItem(iKey,pASDUCache);
                        pASDUCache->iIndexNo = iKey;
                    }else{
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad] m_index103Cache.CountItems key[%d]重复，103Cache覆盖",m_strStnId.c_str(),iKey);
                        time(&pASDUCache->tTimeFlag);
                        m_index103Cache.AttachItem(iKey,pASDUCache);//
                        pASDUCache->iIndexNo = iKey;
                    }
                }else{
                    pASDUCache->bIsFailed = true;
                    pASDUCache->bIsFull = true;
                }
                iCount++;
                if ((iCount == m_iSlideWindows)||(pASDUCacheList.end() - ite) == 1){
                    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] iCount[%d]",m_strStnId.c_str(),iCount);
                    if(IfGetResult((int)(GetTimeOut(210)*0.9),pASDUCache,chLog)){
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] IfGetResult after(pASDUCacheList.end() - ite)[%d]",m_strStnId.c_str(),(pASDUCacheList.end() - ite));
                        bResult = false;
                        ASDULIST::iterator inIte = pASDUCache->in103MsgVector.begin();//拿第一个包
                        if (inIte != pASDUCache->in103MsgVector.end()) {
                            STTP_FULL_DATA resultSttpData;
                            zero_sttp_full_data(resultSttpData);
                            int nFileFullLen = pASDUCache->out103Msg.iReserved;//iReserved 文件全长
                            //resultSttpData.sttp_body.variant_member.file_data.nSize = nFileFullLen;
                            if (1 == m_rASDUHandler.IntermediateResultHandle(*inIte,resultSttpData)) {
                               m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] allLen[%d] RcvLen[%d]",m_strStnId.c_str(),nFileFullLen,resultSttpData.sttp_body.variant_member.file_data.nSize);
                                if ((215==resultSttpData.sttp_head.uMsgID) &&  (1 ==resultSttpData.sttp_body.variant_member.file_data.nOffset)){//offset是14Hor15H
                                    iSentSize = resultSttpData.sttp_body.variant_member.file_data.nSize;
                                    if((pASDUCacheList.end() - ite) == 1){//最后一组窗子 //发215
                                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()]最后一组窗子 ",m_strStnId.c_str());
                                        memcpy(resultSttpData.sttp_body.ch_pt_id, m_strStnId.c_str(), 12);//子站ID
                                        resultSttpData.sttp_body.variant_member.file_data.strFileName = pSttpData.sttp_body.variant_member.file_data.strFileName;
                                        if(nFileFullLen==resultSttpData.sttp_body.variant_member.file_data.nSize){
                                           m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()]最后一组窗子  allLen[%d] RcvLen[%d] 相等ok[%d]",m_strStnId.c_str(),
                                                    nFileFullLen,resultSttpData.sttp_body.variant_member.file_data.nSize,resultSttpData.sttp_body.variant_member.file_data.nOffset);
                                            resultSttpData.sttp_body.variant_member.file_data.nOffset = 1;
                                            bResult = true;
                                        }else{
                                            resultSttpData.sttp_body.variant_member.file_data.nOffset = 0;//0-失败 1-成功
                                           m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()]最后一组窗子  allLen[%d] RcvLen[%d] 不等",m_strStnId.c_str(),nFileFullLen,resultSttpData.sttp_body.variant_member.file_data.nSize);
                                            bResult = false;
                                        }
                                        //Publish00215(resultSttpData);
                                    }else{
                                        memcpy(resultSttpData.sttp_body.ch_pt_id, m_strStnId.c_str(), 12);//子站ID
                                        resultSttpData.sttp_body.nMsgId = 213;//20157该失败回答对应的命令报文ID号;例：如果是对召唤定值20015的超时，则该处填20015
                                        resultSttpData.sttp_head.uMsgRii = resultSttpData.sttp_body.variant_member.file_data.nFileNO;
                                        Publish20157(pSttpData,iSentSize);//防页面超时
                                        bResult = true;
                                    }
                                    
                                }
                            }
                        }
                        if (!bResult) {
                            break;
                        }
                    }else{
                       m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] IfGetResult else ",m_strStnId.c_str());
                        bResult = false;
                        break;
                    } 
                    iCount = 0;
                }
                ite++;
            }
            if (bResult) {
                m_rLogFile.FormatAdd(CLogFile::trace,"文件内容下装处理成功");
                PublishOperationResult(1,iSentSize,pSttpData);
            }else{
                m_rLogFile.FormatAdd(CLogFile::error,"文件内容下装处理失败");
                PublishOperationResult(0,iSentSize,pSttpData);
            }
        }
    }else 
    if (0 == iRet){
        NoSupportedOperation(pSttpData);
        m_rLogFile.FormatAdd(CLogFile::error,"文件内容下装命令不支持，回复20125");
    }else{
        PublishOperationResult(0,0,pSttpData);
        m_rLogFile.FormatAdd(CLogFile::error,"文件内容下装命令处理失败");
    }
    ClearASDUCacheList(pASDUCacheList);
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::ContentDownLoad()] ClearASDUCacheListh()pASDUCacheList[%d]",m_strStnId.c_str(),pASDUCacheList.size());
    return bResult;
}
void CXJPro103ClientWay::PublishOperationResult( int pResult, int pSize,const STTP_FULL_DATA& pSttpCmdMsg )
{    
    STTP_FULL_DATA sSttp = pSttpCmdMsg;
//    CSttpMsgAnalyze pAnalyze(&sSttp);
//    STTPMSG_QUEUE  vMsg;
//    pAnalyze.Data_To_Sttp(vMsg);
//    if(vMsg.size()!=1){
//        m_rLogFile.FormatAdd(CLogFile::error,"%s，回复215 Data_To_Sttp err",getErrorMsg(BUSINESS_ERROR_START_NO+pResult));
//        return ;
//    }
//    STTPMSG SourceMsg=vMsg.front();
//    
//    STTPMSG sttpResultMsg;
//    bzero(&sttpResultMsg,sizeof(STTPMSG));
//    CSttpMsgMaker pSttpMaker;
//    pSttpMaker.Make00215Msg(sttpResultMsg,SourceMsg,pResult,pSize);
//    
//    
//	STTP_FULL_DATA sttp_data;
//	zero_sttp_full_data(sttp_data);
//	CSttpMsgAnalyze pAnalyze2(&sttp_data);
//	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    sSttp.sttp_head.uMsgID = 215;
    sSttp.sttp_body.variant_member.file_data.nOffset = pResult;
    sSttp.sttp_body.variant_member.file_data.nSize = pSize;
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sSttp;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
    m_rLogFile.FormatAdd(CLogFile::trace,"发送文件下装结果发送入队列");
}
void CXJPro103ClientWay::NoSupportedOperation( const STTP_FULL_DATA& pSttpCmdMsg )
{
    STTP_FULL_DATA sSttp = pSttpCmdMsg;
    CSttpMsgAnalyze pAnalyze(&sSttp);
    STTPMSG_QUEUE  vMsg;
    pAnalyze.Data_To_Sttp(vMsg);
    if(vMsg.size()!=1){
        m_rLogFile.FormatAdd(CLogFile::error,"回复215 Data_To_Sttp err[%d]",vMsg.size());
        return ;
    }
    STTPMSG SourceMsg=vMsg.front();
    
    CSttpMsgMaker pSttpMaker;
    STTPMSG sttpResultMsg;
    bzero(&sttpResultMsg,sizeof(STTP_PUBLISH_MSG));
    pSttpMaker.Make20125Msg(sttpResultMsg,SourceMsg,"规约不支持该操作");
    
    STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }	
}
bool CXJPro103ClientWay::IfGetResult(int pTimeOut, ASDUMESSAGE_CACHE_TYPE* pASDUCache , char* pError)
{
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IfGetResult()] iTYP[%d]pTimeOut[%d] ",m_strStnId.c_str(),pASDUCache->out103Msg.iTYP,pTimeOut);
    bool bResult(false);
    if (NULL == pASDUCache)
    {
        return bResult;
    }
    time_t recvTime;

    do 
    {
        MySleep(1);
        if (IfExit())
        {
            m_rLogFile.FormatAdd(CLogFile::error,"收到退出信号，退出等待结果过程！");
            bResult = false;
            break;
        }
        {
            CLockUp localLock(&(pASDUCache->lASDUCacheNode));
            //printf("[%s][CXJPro103ClientWay::IfGetResult()] iTYP[%d] iReserved[%d],full[%d]  failed[%d]",m_strStnId.c_str(),pASDUCache->out103Msg.iTYP,pASDUCache->out103Msg.iReserved,pASDUCache->bIsFull,pASDUCache->bIsFailed);

            //printf("[%s][CXJPro103ClientWay::IfGetResult()]while (1) full[%d]  failed[%d] ",m_strStnId.c_str(),pASDUCache->bIsFull,pASDUCache->bIsFailed);
            
            if (pASDUCache->bIsFull) {
               m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IfGetResult()]is full[%d]  failed[%d] iTYP[%d]",m_strStnId.c_str(),pASDUCache->bIsFull,pASDUCache->bIsFailed,pASDUCache->in103MsgVector.end()->iTYP);
                bResult = !pASDUCache->bIsFailed;
                if (!bResult){
                    m_rLogFile.FormatAdd(CLogFile::error,"子站回复失败！");
                }
                break;
            }else{
                time(&recvTime);
                if ((recvTime - pASDUCache->tTimeFlag) > pTimeOut) {
                    m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::IfGetResult()]等待结果超时！recvTime[%d]tTimeFlag[%d] pTimeOut[%d]",m_strStnId.c_str(),recvTime,pASDUCache->tTimeFlag,pTimeOut);
                    bResult = false;
                    break;
                }
                continue;
            }
        }
    } while (1);

    if (-1 != pASDUCache->iIndexNo)
    {
        m_index103Cache.DetachItem(pASDUCache->iIndexNo);
        m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IfGetResult()]释放iKey[%d]",m_strStnId.c_str(),
                        pASDUCache->iIndexNo);
    }
   m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::IfGetResult()]full[%d]  failed[%d] return bResult[%d]",m_strStnId.c_str(),pASDUCache->bIsFull,pASDUCache->bIsFailed,bResult);
    return bResult;
}
UINT CXJPro103ClientWay::GetTimeOut( UINT uMsgId )
{
    UINT uResult(30);
    switch (uMsgId)
    {
    case 20127:
    case 20129:
        uResult = m_tTimeOut.call_single_config;
        break;
        //召唤操作
    case 20003:    // 保护通讯状态下载要求
    case 20128:    // 总召
        uResult = m_tTimeOut.call_protect_comm;
        break;
    case 20085:    // 录波器通讯状态下载要求
        uResult = m_tTimeOut.call_wav_comm;
        break;
    case 20081:    // 装置运行状态下载要求
        uResult = m_tTimeOut.call_runstatus;
        break;
    case 20007:    // 保护模拟量采样值下载要求
        uResult = m_tTimeOut.call_anai;
        break;
    case 20011:    // 保护开关量采样值下载要求
        uResult = m_tTimeOut.call_switch;
        break;
    case 20015:    // 保护定值下载要求
        uResult = m_tTimeOut.call_protect_settingdata;
        break;
    case 20106:    // 召唤定值区号要求
        uResult = m_tTimeOut.call_zone;
        break;
    case 20108:    // 召唤软压板要求
        uResult = m_tTimeOut.call_softboard;
        break;
    case 20105:    // 下载历史动作事件要求
        uResult = m_tTimeOut.call_history_info;
        break;
    case 20110:    // 保护基本信息下载要求
        uResult = m_tTimeOut.call_basic_info;
        break;
    case 20088:    // 召唤装置时间要求
        uResult = m_tTimeOut.call_dev_time;
        break;
        //文件操作
    case 20025:    // 录波文件列表下载要求
        uResult = m_tTimeOut.call_wavfile_list;
        break;
    case 20042:    // 录波文件下载要求
        uResult = m_tTimeOut.call_waverfile;
        break;
    case 203:	   // 通用文件列表下载要求
        uResult = m_tTimeOut.call_common_file_list;
        break;
    case 210:	   // 通用文件下载要求
        uResult = m_tTimeOut.call_common_file;
        break;
        //控制操作
    case 20047:    // 保护定值区切换预校要求
    case 20055:    // 保护定值区切换执行要求
	case 20169:	   // 保护定值区切换预校要求-通用
	case 20171:    // 保护定值区切换执行要求-通用
        uResult = m_tTimeOut.set_zone;
        break;
    case 20051:    // 保护定值修改要求
    case 20053:    // 保护定值修改执行要求
        uResult = m_tTimeOut.set_setting_data;
        break;
    case 20117:    // 软压板投退预校要求
    case 20113:    // 软压板投退执行要求
        uResult = m_tTimeOut.set_soft_board;
        break;
    case 20057:    // 保护信号复归要求
        uResult = m_tTimeOut.set_signal_reset;
        break;
    case 20059:    // 保护对时要求
        uResult = m_tTimeOut.set_dev_time;
        break;
    case 20123:    // 录波器远方触发下载要求
        uResult = m_tTimeOut.set_wav_remote_touch;
        break;
    default :
        break;
    }
    
    return uResult;
}
void CXJPro103ClientWay::Publish20157( const STTP_FULL_DATA& pSttpData, int pSize)
{
    STTPMSG sttpResultMsg;
    bzero(&sttpResultMsg,sizeof(STTPMSG));

    CSttpMsgMaker pSttpMaker;
    pSttpMaker.Make20157Msg(sttpResultMsg,pSttpData.sttp_head.uMsgRii,
                            pSttpData.sttp_body.ch_pt_id,pSttpData.sttp_body.nCpu,pSttpData.sttp_body.nZone,
                            pSttpData.sttp_head.uMsgID,pSize);
    
    STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&sttpResultMsg);
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}
void CXJPro103ClientWay::Publish00215( const STTP_FULL_DATA& pSttpData)
{
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = pSttpData;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}


//回调--连上才会有
void CXJPro103ClientWay::OnConnectChange( int pStatus,bool bCycleSend ,LPVOID pParam)
{
    CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    if (NULL == pThis){
        return;
    }
    if (pThis->IfExit()) {
        return ;
    }

	
    if(  (pThis->m_bConnected==true)&&(0 == pStatus) )
    {
        pThis->SendCfgPtCommClose();//发未知//断的时候的时候发未知,整站都断了，就不知道哪些设备断了
        //断连复位--因为false不会出现，只有连上的时候会进下这里
        if(pThis->m_bConnected){
            if(pThis->m_bBoot==false) {//断连相当于复位程序
                pThis->m_bBoot=true;
                pThis->m_bCanProxy = false;
                STN_CFG_INFO s1;   pThis->sStnCfgInfoFile=s1;
                STN_MODEL_INFO s2; pThis->sStnMdlInfoFile=s2;
                {
                    CLockUp lockUp(&pThis->m_LockFor860Step);
                    pThis->m_map860IedList.clear();
                }
                
                
                
                pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::OnConnectChange()] step OnConnectChange  断连复位 ",pThis->m_strStnId.c_str());
                {
                    CLockUp lockUp(&pThis->m_LockForCloseStn);
                    pThis->setCloseStn.insert(pThis->m_strStnId);//我发不了完美的通道关闭状态，索性关闭链接直接
                }
                pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::OnConnectChange()] step OnConnectChange  断连复位 准备关闭该站前置",pThis->m_strStnId.c_str());
                
            }
        }
    }
    //一连上发复位通知，比如config错，会刷新不了所有设备状态
    if(  (pThis->m_bConnected==false)&&(1 == pStatus) ){
        pThis->SendCfgPtCommClose();//发未知
    }
    //断的时候的时候发未知,整站都断了，就不知道哪些设备断了
    if(  (pThis->m_bConnected==false)&&(0 == pStatus) ){
        pThis->SendCfgPtCommClose();//发未知
        SecDevFlowMoudle::getInstanse()->resetStnSta(pThis->m_strStnId); // 20231219 lmy add  重置站端所有设备的连接为关闭状态
    }
    
    pThis->m_bConnected = (1 == pStatus)?true:false;
    MySleep(1000);
	pThis->m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::OnConnectChange]  m_bConnected[%d][%d-true %d-false]",pThis->m_strStnId.c_str(),pThis->m_bConnected,true,false);
    //发通知...
    pThis->SendStn20144(pThis->m_bConnected);
    pThis->SendStn20002(pThis->m_bConnected);
}
void CXJPro103ClientWay::SendStn20144(bool nConnected)
{
    STTP_FULL_DATA sttp_data;
    zero_sttp_full_data(sttp_data);
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 20144;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    
    snprintf(sttp_data.sttp_body.ch_station_id,sizeof(sttp_data.sttp_body.ch_station_id),"%s",m_strStnId.c_str());
    //sttp_data.sttp_body.nFlag = 2;//1-1区, 2-2区，3-3区
    sttp_data.sttp_body.nFlag = m_nWorkArea;//1-1区, 2-2区，3-3区
    
    sttp_data.sttp_body.nStatus = (nConnected==true) ? 1:0;//该区的通讯状态值 (0-断开 1-正常)
    sttp_data.sttp_body.nEventType = 10;//10-通道异常:主子站通信链路断开   tb_station_status_config
    {
        CXJTime CvtTime( time(NULL) );
        snprintf(sttp_data.sttp_body.ch_time_12_BIT1,sizeof(sttp_data.sttp_body.ch_time_12_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP12Time).c_str());
    }
    
            
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}
void CXJPro103ClientWay::SendStn20002(bool nConnected)
{
    STTP_FULL_DATA sttp_data;
    zero_sttp_full_data(sttp_data);
    
    sttp_data.sttp_head.uMsgRii = 0;
    sttp_data.sttp_head.uMsgID = 20002;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    
   
    
    STTP_DATA sMngrUnit;
    sMngrUnit.str_value = m_strStnMgrSecDev;//xxBH99999
    sMngrUnit.id = (nConnected==true) ? 0:1;//sttp 0-正常，1-停运,2-未知//该区的通讯状态值 (0-断开 1-正常)
    sMngrUnit.int_reserved = 1;//子站离线  tb_pt_commu_detail
    
    sttp_data.sttp_body.variant_member.value_data.push_back(sMngrUnit);
    {
        CXJTime CvtTime( time(NULL) );
        snprintf(sttp_data.sttp_body.ch_time_12_BIT1,sizeof(sttp_data.sttp_body.ch_time_12_BIT1),"%s",CvtTime.GetTimeString(CXJTime::STTP12Time).c_str());
    }

	if ( 0 == sttp_data.sttp_body.variant_member.value_data.size() )
	{
		return;
	}
    
    BUS_RECV_STTPFULLDATA_INFO sSend;
    sSend.sttp_data = sttp_data;
    sSend.t_time = time(NULL);
    {
        CLockUp lockUp(&m_LockForSendBusList);
        m_SendBusList.push_back(sSend);
    }
}

bool CXJPro103ClientWay::OnRecvASDUMsg( const ASDUMESSAGE& pASDUMsg , LPVOID pParam )
{
    CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    if (NULL == pThis){
        return false;
    }
	if (pThis->IfExit()) {
        return false;
    }
    
    pThis->RecvASDUHandle(pASDUMsg);	
    return true;
}
bool CXJPro103ClientWay::OnSendASDUMsgFail( const ASDUMESSAGE& pASDUMsg , LPVOID pParam )
{
    CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    if (NULL == pThis){
        return false;
    }
	if (pThis->IfExit())
    {
        return false;
    }
    
    pThis->SendASDUFailHandle(pASDUMsg);	
	return true;
}
bool CXJPro103ClientWay::OnSendASDUMsgSuccess( const ASDUMESSAGE& pASDUMsg , LPVOID pParam )
{
    CXJPro103ClientWay* pThis = (CXJPro103ClientWay*)pParam;
    if (NULL == pThis){
        return false;
    }
	if (pThis->IfExit())
    {
        return false;
    }
    
    pThis->SendASDUSeccessHandle(pASDUMsg);	
	return true;   
}

void CXJPro103ClientWay::RecvASDUHandle( const ASDUMESSAGE &ASDUMessage )
{		
    if (m_rASDUHandler.IsAutoUp(ASDUMessage)) { 
        CLockUp localLock(&m_AutoUpASDULock);//iIsAutoUp=1 为突发信息
		if (m_AutoUpASDUBuf.size()>MAX_RESULT_QUEUE_SIZE) {
			m_AutoUpASDUBuf.pop_front();
			m_rLogFile.FormatAdd(CLogFile::error,"autoup queue is full,pop the front node");
		}

        m_AutoUpASDUBuf.push_back(ASDUMessage);
        m_rLogFile.FormatAdd(CLogFile::trace,"收到自动上送的ASDU，type=%d，放入自动上送队列",ASDUMessage.iTYP);
    }else{
        if (MatchASDUCommand(ASDUMessage)) {
            m_rLogFile.FormatAdd(CLogFile::trace,"收到的ASDU%d 匹配到命令，放入对应的结果队列",ASDUMessage.iTYP);
        } else {
            m_rLogFile.FormatAdd(CLogFile::trace,"收到的ASDU%d 没有匹配到命令",ASDUMessage.iTYP);
        }
    }
}
bool CXJPro103ClientWay::MatchASDUCommand( const ASDUMESSAGE& ASDUMsg )
{
    bool bResult(false);
    
    if (0 == ASDUMsg.iTYP){
        return bResult;
    }
    
    try
    {	
        if (ASDUMsg.INFSET.size() > 0){
            int nKey = m_rASDUHandler.GetKey(ASDUMsg);
          // m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::MatchASDUCommand]key[%d]",m_strStnId.c_str(),nKey);
            ASDUMESSAGE_CACHE_TYPE* p103ASDU = m_index103Cache.FindItem(nKey);
            if (p103ASDU != NULL) {
                
                CLockUp localLock(&(p103ASDU->lASDUCacheNode));
                time(&p103ASDU->tTimeFlag);
                p103ASDU->in103MsgVector.push_back(ASDUMsg);
                p103ASDU->iSize += ASDUMsg.INFSET.size();
                if (0x2a == p103ASDU->out103Msg.iCOT){
                    if (0x2a == ASDUMsg.iCOT){
                        p103ASDU->bIsFailed = false;
                    }else{
                        p103ASDU->bIsFailed = true;
                        p103ASDU->bIsFull = true;
                    }
                }else{
                    p103ASDU->bIsFailed = false;
                }
                
                
                
                if (m_rASDUHandler.IsEndFrame(ASDUMsg)) {
                    //m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::MatchASDUCommand]m_rASDUHandler.IsEndFrame  bIsFull=true",m_strStnId.c_str());
                    p103ASDU->bIsFull = true;
                }
                bResult = true;
                m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::MatchASDUCommand]iKey[%d] get bResult[%d]p103ASDU->bIsFull[%d] ",m_strStnId.c_str(),nKey,bResult,p103ASDU->bIsFull);
            }
            else{ //指针为空时 
               m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::MatchASDUCommand]key[%d] not get bResult[%d] ",m_strStnId.c_str(),nKey,bResult);
                bResult = false;
            }
        }
        else{
            m_rLogFile.FormatAdd(CLogFile::error,"收到的ASDU中信息元素集为空，无效的ASDU丢弃");
            bResult = false;
            m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::MatchASDUCommand]bResult[%d]   ASDUMsg.INFSET.size()[%d] ",m_strStnId.c_str(),bResult,ASDUMsg.INFSET.size());
        }
    }
    catch(...){
        m_rLogFile.FormatAdd(CLogFile::error,"收到的ASDU插入接收缓存时发生异常");
        bResult = false;
    }
    return bResult;	
}
void CXJPro103ClientWay::SendASDUFailHandle( const ASDUMESSAGE &ASDUMessage )
{
    bool bResult(false);
    
    if (0 == ASDUMessage.iTYP)
    {
        return ;
    }
    try
    {    
        if (ASDUMessage.INFSET.size() > 0){
            int nKey = m_rASDUHandler.GetKey(ASDUMessage);
            ASDUMESSAGE_CACHE_TYPE* p103ASDU = m_index103Cache.FindItem(nKey);
            if (NULL != p103ASDU)
            {
                CLockUp localLock(&(p103ASDU->lASDUCacheNode));
                p103ASDU->bIsFull = true;
                p103ASDU->bIsFailed = true;
            }
        }
    }
    catch(...)
    {
        m_rLogFile.FormatAdd(CLogFile::error,"ASDU发送失败处理发生异常");
    }
    return ;	
}
void CXJPro103ClientWay::SendASDUSeccessHandle( const ASDUMESSAGE &ASDUMessage )
{
    bool bResult(false);
    
    if (0 == ASDUMessage.iTYP)
    {
        return ;
    }
    try
    {    
        if (ASDUMessage.INFSET.size() > 0){
            int nKey = m_rASDUHandler.GetKey(ASDUMessage);
            ASDUMESSAGE_CACHE_TYPE* p103ASDU = m_index103Cache.FindItem(nKey);
            if (NULL != p103ASDU)
            {
                CLockUp localLock(&(p103ASDU->lASDUCacheNode));
                p103ASDU->bIsFull = true;
                p103ASDU->bIsFailed = false;
                p103ASDU->in103MsgVector.push_back(p103ASDU->out103Msg);
            }
        }
    }
    catch(...)
    {
        m_rLogFile.FormatAdd(CLogFile::error,"ASDU发送失败处理发生异常");
    }
    return ;	
}


void CXJPro103ClientWay::ClearASDUCacheList( ASDUCACHETYPE &pASDUCacheList )
{
    ASDUMESSAGE_CACHE_TYPE* pASDUCache = NULL;
    ASDUCACHETYPE::iterator ite = pASDUCacheList.begin();
    while (ite != pASDUCacheList.end())
    {
        if (NULL != ite->second){
            pASDUCache = ite->second;
            delete pASDUCache;
            ite->second = NULL;
        }
        ++ite;
    }
    pASDUCacheList.clear();
}


char * CXJPro103ClientWay::__CvtGbk2Utf8(const char * cObj)
{
	memset(m_cChr,0,_MAX_CHAR_BUFF_LEN);
    if(cObj!=NULL){
        m_CharCvtObj.convert_gbk_utf8(cObj,m_cChr,_MAX_CHAR_BUFF_LEN-1);
    }
	return m_cChr;
}
char * CXJPro103ClientWay::__CvtUtf82Gbk(const char * cObj)
{
	memset(m_cChr,0,_MAX_CHAR_BUFF_LEN);
    if(cObj!=NULL){
        m_CharCvtObj.convert_utf8_gbk(cObj,m_cChr,_MAX_CHAR_BUFF_LEN-1);
    }
	return m_cChr;
}
bool CXJPro103ClientWay::InitGbkU8()
{
	if ( !m_CharCvtObj.load_lib()) {
		m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::InitGbkU8()] 加载 字符集编码转换接口库失败.",m_strStnId.c_str());
		return false;
	}
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::InitGbkU8()] 加载 字符集编码转换接口库 ok.",m_strStnId.c_str());
	return true;
}
bool CXJPro103ClientWay::UnInitGbkU8()
{
	if ( !m_CharCvtObj.unload_lib()) {
		m_rLogFile.FormatAdd(CLogFile::error,"[%s][CXJPro103ClientWay::UnInitGbkU8()] 卸载 字符集编码转换接口库失败.",m_strStnId.c_str());
		return false;
	}
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::UnInitGbkU8()] 卸载 字符集编码转换接口库 ok.",m_strStnId.c_str());
	return true;
}
int CXJPro103ClientWay::PrintBytes(const string &str)
{
    if(!m_bLogDebug) return 0;
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),str.begin(),str.end());
    
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PrintBytes2]",m_strStnId.c_str());
    string str1;str1.clear();
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)*it);
        str1=str1+c20;
    }
    m_rLogFile.FormatAdd(CLogFile::trace,"%s",str1.c_str());
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::PrintBytes2]  end ",m_strStnId.c_str());
}
int CXJPro103ClientWay::PrintBytes( char *message,int message_len)
{
    if(!m_bLogDebug) return 0;
   m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::PrintBytes2]");
   string str1;str1.clear();
    for(int i=0;i<message_len;i++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)message[i]);
        str1=str1+c20;
    }
   m_rLogFile.FormatAdd(CLogFile::trace,"%s",str1.c_str());
   m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::PrintBytes]  end ");
}
int CXJPro103ClientWay::PrintBytes( unsigned char *message,int message_len)
{
    if(!m_bLogDebug) return 0;
   m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::PrintBytes2]");
   string str1;str1.clear();
    for(int i=0;i<message_len;i++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)message[i]);
        str1=str1+c20;
    }
   m_rLogFile.FormatAdd(CLogFile::trace,"%s",str1.c_str());
   m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::PrintBytes]  end ");
}

bool CXJPro103ClientWay::checkAbort(const string &strMsg,int nChNo)
{
    if(strMsg.length()<19)      return false;
  
    BYTE cSPDU = strMsg.at(7);  //SPDU Type: ABORT (AB) SPDU (25)
    if (cSPDU == 0x19)  
    {
	BYTE cParaType = strMsg.at(9);  // Parameter type: Transport_Disconnect (17)
	BYTE cParaLen = strMsg.at(10);  // Parameter length: 1
	BYTE cParaFlag = strMsg.at(11); // Flags: 0x03, Transport connection, User abort

	if ((cParaType == 0x11) && (cParaLen == 0x1) && (cParaFlag == 0x03))
        {
             m_rLogFile.FormatAdd(CLogFile::error,"[%s] [nChNo:%d] [CXJPro103ClientWay::checkAbort] mms 保护设备用户中断，msgLen %d ",m_strStnId.c_str(),nChNo,strMsg.length());
             handleAbort(nChNo);// 改变状态并通知总线并发送重连
             return true;
        }
    }
    return false;
}

void CXJPro103ClientWay::handleAbort(int nChNo)
{
    CLockUp lockUp(&m_LockFor860Step);
    map<int,STEP_INFO>::iterator it1=m_map860IedList.find(nChNo);
    if(it1!=m_map860IedList.end())
    {
        it1->second.nFailCount = (it1->second.nFailCount>65535) ? 65535 : (it1->second.nFailCount+1);
        SendPtChNo20002(nChNo,1);
        SendAsdu200Close103Conn(nChNo, 1);
		it1->second.nStep = LinkClosed;
         m_rLogFile.FormatAdd(CLogFile::error,"[%s] [nChNo:%d] [CXJPro103ClientWay::handleAbort] mms 保护设备用户中断，发送重连命令 ",m_strStnId.c_str(),nChNo);
     }
}

bool CXJPro103ClientWay::isConnectChNo(int nChNo)
{
   CLockUp lockUp(&m_LockFor860Step);
   map<int,STEP_INFO>::iterator it =  m_map860IedList.find(nChNo);
   if(it!= m_map860IedList.end())
   {
      return (RcvInitOk == it->second.nStep);
   }
   
   return false;
}


 //ch_pt_id: 通道号， nCmdType：命令类型（3抓包打开，4 抓包关闭)
int CXJPro103ClientWay::SendAsdu200TcCap(int nCmdType,int nRii)
{
    m_rLogFile.FormatAdd(CLogFile::trace,"[%s][CXJPro103ClientWay::SendAsdu200TcCap，命令类型:%d]",m_strStnId.c_str(),nCmdType);
    if( nCmdType >4 || nCmdType<3 ) return -1;
    
    STTP_FULL_DATA sttp_data;  
    zero_sttp_full_data(sttp_data);
    
    sttp_data.sttp_head.uMsgRii = nRii;
    sttp_data.sttp_head.uMsgID = 40001;
    sttp_data.sttp_head.uMsgType = 'I';
    sttp_data.sttp_head.uMsgLength = 0;
    sttp_data.sttp_head.uMsgLengthType = 40001;
    sttp_data.sttp_head.uMsgEndFlag = 0;
    
    sttp_data.sttp_body.nCpu = 0;//通道编号
    sttp_data.sttp_body.nFlag = GetFrameNo();//帧序号
    sttp_data.sttp_body.nSource = 0;//规约类型
    sttp_data.sttp_body.nZone = 0;//压缩标志
    sttp_data.sttp_body.nEventType = nCmdType;//命令类型
    sttp_data.sttp_body.nStatus = 0;//数据包字节数
    sttp_data.sttp_body.nCmdSource = ParketProxy;//包类型
    //sttp_data.sttp_body.strMessage = "";//数据包
    
    BUS_RECV_STTPFULLDATA_INFO Sttp_Info;
    Sttp_Info.sttp_data = sttp_data;
    Sttp_Info.t_time = time(NULL);
   
    {
        CLockUp lockUp(&m_LockForRcvBusInfList);
        m_RcvBusInfList.push_back(Sttp_Info);
    }
    
    return 0;
    
}

void CXJPro103ClientWay::GetModleFile(STTPMSG &m_pSttpCacheNode)
{
	STTP_FULL_DATA sttp_data;
	zero_sttp_full_data(sttp_data);
	CSttpMsgAnalyze pAnalyze2(&sttp_data);
	pAnalyze2.Sttp_To_Data(&m_pSttpCacheNode);

    MODEL_FILE_STRUCT model_file;
    std::string sFileName = "";
    for(int i=0; i<sttp_data.sttp_body.variant_member.file_list.size(); i++)
    {
        CXJTime xjtm;
        xjtm.AssignTimeString(sttp_data.sttp_body.variant_member.file_list[i].ch_time,CXJTime::STTP19Time);
        //sFileName = __CvtUtf82Gbk(sttp_data.sttp_body.variant_member.file_list[i].strFielName.c_str());
        sFileName =sttp_data.sttp_body.variant_member.file_list[i].strFielName.c_str();
		if(xjtm.Time_t() == model_file.ch_time)
		{
			if(sFileName.find("model.xml")!= std::string::npos || sFileName.find("Model.xml")!= std::string::npos)
			{
				model_file.ch_time = xjtm.Time_t();
				model_file.sFileName = sFileName;
			}
			m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::SetModelFile]  文件名:%s ,文件时间:%s,有相同文件时间的文件存在 ",sFileName.c_str(),sttp_data.sttp_body.variant_member.file_list[i].ch_time);
		}
		else if(xjtm.Time_t() >model_file.ch_time)
		{
			model_file.ch_time = xjtm.Time_t();
			model_file.sFileName = sFileName;
		}
        m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::SetModelFile]  文件名:%s ,文件时间:%s,%d --- %d",sFileName.c_str(),sttp_data.sttp_body.variant_member.file_list[i].ch_time,model_file.ch_time,xjtm.Time_t());
    }
    if(model_file.ch_time >0 )
    {
        // 召读模型文件
        CallModelXmlFile(model_file.sFileName);
        m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::SetModelFile] 模型文件名:%s ,文件时间:%d ", model_file.sFileName.c_str(), model_file.ch_time);
    }
    else
    {
        m_rLogFile.FormatAdd(CLogFile::trace,"[CXJPro103ClientWay::SetModelFile] 模型文件名查找不到 ");
    }
    m_bStartCallModel = false;
}

void CXJPro103ClientWay::SetWorkArea(int nArea)
{
    m_nWorkArea = nArea;
}

 void CXJPro103ClientWay::SetModFileCallType(int nType)
 {
     m_nModFileCallType = nType;
 }
