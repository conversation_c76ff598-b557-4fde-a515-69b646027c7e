# 新增
## ASDU4
    启动字符	68H
    长度L（2个字节）	    
    控制域（4个字节）
    类型标识（TYP）	04H
    可变结构限定词（VSQ）	81H
    传送原因（COT）	01H
    ASDU地址（2个字节）	  
    功能类型（FUN）	
    信息序号（INF）	
    故障距离（SCL）	
    相对时间RET（2个字节）    
    故障序号FAN（2个字节）  
    7字节时标 CP56Time2a	

## ASDU20
通用分类命令：远方复归二次设备指示灯
主站：
    启动字符	68H
    长度L	
    控制域	
    类型标识(TYP)	14H
    可变结构限定词(VSQ)	81H
    传送原因(COT)	14H
    ASDU地址（2个字节）	
    功能类型(FUN)	FFH
    信息序号(INF)	13H
    返回信息标识符(RII)	
子站：
    启动字符	68H
    长度L		
    控制域	
    类型标识(TYP)	01H
    可变结构限定词(VSQ)	81H
    传送原因(COT)	14H/15H 
    ASDU地址（2个字节）		
    功能类型(FUN)	FFH
    信息序号(INF)	13H
    双点信息	DPI
    7字节时标 CP56Time2a	
    附加信息（SIN）	填写主站下发复归帧中的RII
说明：
1.远方复归命令，要求子站做出回答，由于二次设备一般都不对复归命令做出响应，因此子站回复主站时，仅需要向主站表明，复归命令是否被正确传送到了二次设备。
2.ASDU地址的高8位为0时，表示对子站本身进行复归，为1-254时，表示对子站内某一装置进行复归，为255时，表示同时对该子站内所有装置进行复归。
3. 复归信号灯需要保护设备支持，对不支持复归操作的保护，子站可以不做操作，仅仅回复命令的否定认可。
4.如果二次设备的软压板配置中已经存在“装置复归”信号点，远方复归操作也可以通过软压板投退按照通用分类命令的方式来实现。
# 修改
## ASDU12
故障录波文件名一般建议以故障时间（到秒）为文件名，如20010203143254。
不需要修改，不在子站端实现
## ASDU10
装置当前运行定值区变化信息，与故障量信息通过"返回信息标识符(RII)"进行区分， 故障量信息帧的返回信息标识符填253（FDH），当前运行定值区变化信息帧的返回信息标识符填254（FEH）

**实现说明：**
1. 在ASDU10_INFO结构中添加了nRii字段，用于存储返回信息标识符
2. 修改了_CvtSoftReportToPro函数，根据消息类型设置不同的RII值：
   - NX_IED_EVENT_SOFTTRAP_REPORT (软压板事件): RII = 0xFD (253)
   - NX_SYS_EVENT_IED_SGZONE_CHG_REPORT (定值区变化): RII = 0xFE (254)
3. 修改了_CvtZoneChgReportToPro函数，为定值区变化信息设置RII = 0xFE (254)
4. 修改了FormatAsdu10BodyAutoUp函数，使用ASDU10_INFO中的nRii字段而不是硬编码的0
5. 在日志中添加了RII值的显示，便于调试和验证

## ASDU7

国网103/104（代码中的 GWS 实现）在“全站总召”时，按规约要求上送的是“通信状态”和“运行状态”两类遥信，分别使用不同的功能类型（通信状态通常为 255，运行状态为 251），并通过 ASDU42 打包发送，最后以 ASDU8 结束。

运行状态的功能类型为 251（0xFB），DPI 按运行态/非运行态映射：运行=2，非运行/测试/调试/不可达等=1；信息序号 INF 为设备103地址。

通信状态的功能类型为 255（且在头文件注释中明确“fun 统一为 255，inf=103地址，dpi=通信状态”），DPI 表示有/无通信。 示例（运行状态构造，nFun=251，nInf=IED 的103地址，nDpi由运行态映射）：

福建103 的新增需求是：将“装置当前运行定值区”也作为一组“特殊遥信”，要求在“总召”时上送，且在变化时以 ASDU1 自发上送；建议编码为功能类型 250（0xFA），信息序号为装置地址，DPI 填当前定值区号。
	
现有流程是：
MakeAsdu7InfoList（通信状态或运行状态）→ FormatAsdu42Body
MakeAsdu7InfoList_runstatus（运行状态）→ FormatAsdu42Body
MakeAsdu8Body 结束
现在需要在 1) 和 2) 之间或 2) 和 3) 之间，再插入一帧“定值区遥信”的 ASDU42：
新增一个函数，比如 MakeAsdu7InfoList_settinggroup，遍历全站 IED，生成 InfoObj：
InfoObj.nFun = 250（0xFA）
InfoObj.nInf = IED 的 103 地址（n_outaddr103）
InfoObj.nDpi = 当前定值区号（注意 DPI 的值域，见下“注意事项”）
拿这组 InfoObj 再调用一次 pAsdu42->FormatAsdu42Body
	
## ASDU1
还是上方的
需要_CvtNxEventSubFiledToAsdu1Info中识别“定值区变为事件类型”
```
else if (nEventType == NX_IED_EVENT_SETGROUP_REPORT) {
  // 地址
  Asdu1.Addr.nAddr = pIed->n_outaddr103;
  Asdu1.Addr.nCpu  = ite->n_sub_obj_id;
  // 映射 fun/inf/dpi
  bGetInfoObj = __DoGetSetGroupInfoObj(&(*ite), InfoObj);
}
```
新增一个取 InfoObj 的函数（如 __DoGetSetGroupInfoObj），专门把事件字段映射到 fun/inf/dpi
```
bool TNXEcProAsdu1GWS::__DoGetSetGroupInfoObj(IN NX_EVENT_FIELD_STRUCT* pField, OUT ASDU_INFO_OBJ& Info){
  if (!pField) return false;
  Info.nFun = 250;               // 0xFA
  Info.nInf = /* 已在外层设置为 n_outaddr103 或此处再取 */;
  Info.nDpi = pField->n_value;   // 定值区号，必要时做 0..3 限幅
  return true;
}
```


## ASDU17
国网事件类型取值
1	故障动作信息（带故障量）
2	装置自检信息 
3	开关量变位
FFH	本装置的所有事件
福建事件类型取值
1	保护动作信号（带故障量）
2	装置告警信息 
3	开关量变位
4	软压板状态
5	通信状态
6	硬压板信息
7	保护启动信号（带故障量）
8	保护出口信号（带故障量）
9	保护重合闸信号（带故障量）
10	保护复归信号（带故障量）
FFH	本装置的所有事件

当主站召唤的事件类型为1（保护动作信号）时，子站上送所有保护动作类（保护动作信号、保护启动信号、保护出口信号、保护重合闸信号、保护复归信号）相关的历史信息，且子站上送的信息中，需标明信息所属的准确类型。当主站召唤的事件类型为7（保护启动信号）、8（保护出口信号）、9（保护重合闸信号）、10（保护复归信号）时，子站仅上送对应类型的事件信息。当主站召唤的事件类型为3（开关量变位信息）时，子站上送所有开关类信息（开关量变位信息、软压板状态、通信状态、硬压板信息）相关的历史信息，且子站上送的信息中，需标明信息所属的准确类型。当主站召唤的事件类型为4（软压板状态）、5（通信状态）、6（硬压板信息）时，子站仅上送对应类型的事件信息。

当主站下发召唤装置的历史“保护动作信号”、“保护启动信号”、“保护出口信号”、“保护重合闸信号”、“保护复归信号”时，子站除回应该装置的动作事件外，同时将该装置动作时产生的“故障量”信息上送，若装置没有“故障量”信息时则将对应故障量个数位设为0,“故障量”信息不进行单独召唤。


主站：ASDU17（主站召唤故障历史信息）
表 55 主站召唤故障历史信息
启动字符	68H
长度L	
	
控制域	
	
	
	
类型标识(TYP)	11H
可变结构限定词(VSQ)	81H
传送原因(COT)	14H
ASDU地址（2个字节）	00
	
功能类型(FUN)	FFH
信息序号(INF)	00H/01H
返回信息标识符(RII)	1字节
事件类型	1字节
开始时间	CP56Time2a（7字节）
结束时间	CP56Time2a（7字节）
表 56事件类型取值
取值	事件类型
1	保护动作信号（带故障量）
2	装置告警信息 
3	开关量变位
4	软压板状态
5	通信状态
6	硬压板信息
7	保护启动信号（带故障量）
8	保护出口信号（带故障量）
9	保护重合闸信号（带故障量）
10	保护复归信号（带故障量）
FFH	本装置的所有事件
子站：ASDU18（子站上送故障历史信息）  
表 57 子站上送装置故障历史信息
启动字符	68H
长度L	
	
控制域	
	
	
	
类型标识（TYP）	12H
可变结构限定词（VSQ）	81H
传送原因（COT）	09H
ASDU地址（2个字节）	对应实际cpu信息
	
功能类型（FUN）	FFH
信息序号（INF）	00H/01H
RII	
后续位标志	
开始时间CP56Time2a	
结束时间CP56Time2a	
历史信息条数（1字节）	
历史信息（多个数据块）	...
说明：
1．后续位标志：为0，表示ASDU18传输完毕。
2．历史信息由多个数据块组成，每个数据块表示1条信息，数据块的结构如下表所示。
表 58 历史信息数据块定义
字节	报文内容	说  明
1	事件类型	<1>保护动作信号
		<2>装置告警信息
		<3>开关量变位信息
		<4>软压板状态
		<5>通信状态
		<6>硬压板信息
		<7>保护启动信号
		<8>保护出口信号
		<9>保护重合闸信号
		<10>保护复归信号
2	功能类型	与初始化配置信息时上送点表相对应
3	信息序号	与初始化配置信息时上送点表相对应
4	双点信息DPI	
5-6	相对时间RET	0-65535ms(2个字节的二进制数),
7-8	故障序号FAN	用于识别和继电保护功能相关的一次事件，发生一次事件，故障序号加1, 无意义时填-1
9-15	故障时间	CP56Time2a
16-22	子站接收时间	CP56Time2a
23	故障量个数	本次故障装置所产生的故障量信息个数;
24	组号(Group)	故障量信息对应的组号, 与初始化配置信息时上送点表相对应
25	条目号(Item)	故障量信息对应的条目号, 与初始化配置信息时上送点表相对应
26	KOD	1 -表示实际值
27	数据类型	参照标准103定义
28	数据宽度	根据数据类型定义
29	数据数目	参照标准103定义
30	故障量值	故障量信息的实际值

3．历史信息数据块中的双点信息、相对时间、故障序号和故障相别(JPT)的定义参考ASDU2以及ASDU12(本规范5.3.2)。
4．历史数据块中无效字节采用0xFFH。
5. ASDU17报文中的“起始时间”和“结束时间”所标志的时间段指子站接收信息的时间段，子站在本地查找事件时要以“子站接收信息时间”段进行查询。
6. 子站对事件类型的划分应与保护配置的遥信量类型划分保持一致，参见“7.6 状态量配置”。
7.当主站召唤的事件类型为1（保护动作信号）时，子站上送所有保护动作类（保护动作信号、保护启动信号、保护出口信号、保护重合闸信号、保护复归信号）相关的历史信息，且子站上送的信息中，需标明信息所属的准确类型。当主站召唤的事件类型为7（保护启动信号）、8（保护出口信号）、9（保护重合闸信号）、10（保护复归信号）时，子站仅上送对应类型的事件信息。当主站召唤的事件类型为3（开关量变位信息）时，子站上送所有开关类信息（开关量变位信息、软压板状态、通信状态、硬压板信息）相关的历史信息，且子站上送的信息中，需标明信息所属的准确类型。当主站召唤的事件类型为4（软压板状态）、5（通信状态）、6（硬压板信息）时，子站仅上送对应类型的事件信息。
8. 当主站下发召唤装置的历史“保护动作信号”、“保护启动信号”、“保护出口信号”、“保护重合闸信号”、“保护复归信号”时，子站除回应该装置的动作事件外，同时将该装置动作时产生的“故障量”信息上送，若装置没有“故障量”信息时则将对应故障量个数位设为0,“故障量”信息不进行单独召唤。
9. INF=0 表示召唤子站本地保存的历史事件；INF=1表示直接对装置召唤历史事件。
