﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{9d4cf03f-946f-4ac7-af83-44c666e02324}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXEcModelMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\nx_common\load_nxdbm_lib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\nx_common\nx_model_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\ec_common_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\INXEcModelMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_common\INXEcSSModelSeek.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEcSSModelSeek.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\nx_common\load_nxdbm_lib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GetEcModelIns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcSSModelSeek.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEcModelMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_model_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_model_access.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>