# ASDU4新增设计方案

## 需求分析

根据福建103规约的新增需求，需要实现ASDU4（故障距离信息）的处理功能，具体要求：

### ASDU4报文格式
```
启动字符        68H
长度L（2个字节）    
控制域（4个字节）
类型标识（TYP）    04H
可变结构限定词（VSQ） 81H
传送原因（COT）    01H
ASDU地址（2个字节）  
功能类型（FUN）    
信息序号（INF）    
故障距离（SCL）    
相对时间RET（2个字节）    
故障序号FAN（2个字节）  
7字节时标 CP56Time2a
```

### 功能特点
1. **故障距离信息上送**: 当发生故障时，上送故障距离、相对时间、故障序号等信息
2. **时标支持**: 支持7字节CP56Time2a时标格式
3. **事件驱动**: 基于故障事件自发上送

### ⚠️ **重要发现**
**工作区中没有专门的故障测距事件定义**。现有的事件类型主要包括：
- `NX_IED_EVENT_EVENT_REPORT` - 一般事件（可复用）
- `NX_IED_EVENT_ALARM_REPORT` - 告警事件
- 其他开关量、通信、录波等事件

**建议的事件处理方案**：
1. **复用现有事件**：使用 `NX_IED_EVENT_EVENT_REPORT` 来传输故障测距数据
2. **新增事件类型**：定义专门的 `NX_IED_EVENT_FAULT_DISTANCE_REPORT` 事件类型
3. **扩展现有机制**：基于ASDU2中的故障处理机制进行扩展

## 现状分析

### 现有ASDU4基类
- ✅ **基类存在**: `TNXEcProAsdu4` 已在 `pro/ec_pro_common_iec60870/` 中定义
- ✅ **数据结构**: `ASDU2_4_12_INFO` 结构体已定义，支持ASDU4所需的所有字段
- ✅ **报文格式化**: `FormatAsdu4Body()` 方法已实现完整的ASDU4报文打包逻辑
- ❌ **福建103特化**: 缺少福建103项目的ASDU4_GWS特化实现
- ❌ **事件处理**: 基类的 `ConvertEventMsgToPro()` 方法未实现（返回不支持）

### 工作区现有模式
通过分析工作区代码，发现福建103项目的标准模式：
- 基类位于: `pro/ec_pro_common_iec60870/`
- 福建103特化类位于: `pro/nx_ec_pro_srv_fj103/`
- 命名规则: `TNXEcProAsduX_GWS` 继承自 `TNXEcProAsduX`

## 解决方案

### 实现方案

需要创建福建103项目的ASDU4特化实现，继承现有的基类。

#### 1. 新增文件

**文件1: NXEcProAsdu4_GWS.h**
```cpp
/**********************************************************************
* NXEcProAsdu4_GWS.h         author:your_name     date:current_date            
*---------------------------------------------------------------------
*  note: 福建103 ASDU4报文转换处理头文件                                                                 
*  
**********************************************************************/

#ifndef _H_NXECPROASDU4_GWS_H_ 
#define _H_NXECPROASDU4_GWS_H_

#include "NXEcProAsdu4.h"

/**
* @defgroup  福建103 TNXEcProAsdu4GWS:ASDU4报文转换处理结点类
* @{
*/
 
class TNXEcProAsdu4GWS:public TNXEcProAsdu4
{
    ///////////////////////////////////////////////////////////////构造、析构
public:
    
    /**
    * @brief         析构函数
    * @param[in]     无 
    * @param[out]    无
    * @return        无
    */
    virtual ~TNXEcProAsdu4GWS();

    /**
    * @brief         构造函数 
    * @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
    * @param[out]    CLogRecord * pLogRecord:日志对象指针
    * @return        无
    */
    TNXEcProAsdu4GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

    ///////////////////////////////////////////////////////////////公用方法
public:
    
    /**
    * @brief         根据NX事件信息生成规约事件列表
    * @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
    * @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
    * @return        int 0-成功 其它失败
    */
    virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody);

    ////////////////////////////////////////////////////////////////////////保护方法
protected:
    
    /**
    * @brief         转换NX事件子集列表格式为ASDU4信息结构
    * @param[in]     NX_EVENT_MESSAGE * pMsg:事件消息指针
    * @param[out]    ASDU2_4_12_INFO & Asdu4Info :ASDU4信息结构
    * @return        int 0-成功 其它失败
    */
    virtual int _CvtNxEventSubFieldToAsdu4Info(IN NX_EVENT_MESSAGE * pMsg, OUT ASDU2_4_12_INFO & Asdu4Info);

private:
    
    /**
    * @brief         获得故障距离事件信息体标识对象信息
    * @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
    * @param[out]    ASDU_INFO_OBJ & InfoObj:信息对象
    * @return        bool:true-成功 false-失败
    */
    bool __DoGetFaultDistanceInfoObj(IN NX_EVENT_FIELD_STRUCT * pField, OUT ASDU_INFO_OBJ & InfoObj);

    /**
    * @brief         从事件字段中提取故障信息
    * @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
    * @param[out]    FAULT_INFO & FaultInfo:故障信息
    * @return        bool:true-成功 false-失败
    */
    bool __ExtractFaultInfoFromField(IN NX_EVENT_FIELD_STRUCT * pField, OUT FAULT_INFO & FaultInfo);
};

/** @} */ //OVER

#endif  // _H_NXECPROASDU4_GWS_H_
```

**文件2: NXEcProAsdu4_GWS.cpp**
```cpp
/**********************************************************************
* NXEcProAsdu4_GWS.cpp         author:your_name     date:current_date            
*---------------------------------------------------------------------
*  note: 福建103 ASDU4报文转换处理实现文件                                                             
*  
**********************************************************************/

#include "NXEcProAsdu4_GWS.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu4GWS::~TNXEcProAsdu4GWS()
{
}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu4GWS::TNXEcProAsdu4GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
    :TNXEcProAsdu4(pSeekIns,pLogRecord)
{
    // 设置类名称
    _SetLogClassName("TNXEcProAsdu4GWS");
}

/**
* @brief         根据NX事件信息生成规约事件列表
* @param[in]     NX_EVENT_MESSAGE * pMsg :事件信结构指针
* @param[out]    PRO_FRAME_BODY_LIST  & lBody :规约信息体
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu4GWS::ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody)
{
    char cError[255] = "";
    
    if (pMsg == NULL)
    {
        RcdErrLogWithParentClass("ConvertEventMsgToPro():输入的事件消息指针为NULL","TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }

    // 设置事件消息指针
    m_pEventMsg = pMsg;
    
    ASDU2_4_12_INFO Asdu4Info;
    
    // 转换事件到ASDU4信息结构
    int nRet = _CvtNxEventSubFieldToAsdu4Info(pMsg, Asdu4Info);
    if (nRet < 0)
    {
        sprintf(cError,"ConvertEventMsgToPro():转换事件到ASDU4信息结构失败,ret=%d",nRet);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu4GWS");
        return nRet;
    }
    
    // 格式化ASDU4报文体
    nRet = FormatAsdu4Body(Asdu4Info, lBody);
    if (nRet < 0)
    {
        sprintf(cError,"ConvertEventMsgToPro():格式化ASDU4报文体失败,ret=%d",nRet);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu4GWS");
        return nRet;
    }
    
    sprintf(cError,"ConvertEventMsgToPro():ASDU4事件转换成功,生成%d个报文体",lBody.size());
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu4GWS");
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         转换NX事件子集列表格式为ASDU4信息结构
* @param[in]     NX_EVENT_MESSAGE * pMsg:事件消息指针
* @param[out]    ASDU2_4_12_INFO & Asdu4Info :ASDU4信息结构
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu4GWS::_CvtNxEventSubFieldToAsdu4Info(IN NX_EVENT_MESSAGE * pMsg, OUT ASDU2_4_12_INFO & Asdu4Info)
{
    char cError[255] = "";
    
    if (pMsg->list_subfields.empty())
    {
        RcdErrLogWithParentClass("_CvtNxEventSubFieldToAsdu4Info():事件子字段列表为空","TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 获得变电站地址
    const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
    if( pStation == NULL )
    {
        RcdErrLogWithParentClass("_CvtNxEventSubFieldToAsdu4Info():获取变电站配置失败","TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 获取第一个事件字段（通常故障事件只有一个主要字段）
    NX_EVENT_FIELD_STRUCT & eventField = pMsg->list_subfields.front();
    
    // 获取IED配置
    const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByID(eventField.n_obj_id);
    if( pIed == NULL )
    {
        sprintf(cError,"_CvtNxEventSubFieldToAsdu4Info():获取IED配置失败,IED_ID=%d",eventField.n_obj_id);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 设置地址信息
    Asdu4Info.Addr.nSubstationAdd = pStation->n_outaddr103;
    Asdu4Info.Addr.nAddr = pIed->n_outaddr103;
    Asdu4Info.Addr.nCpu = eventField.n_sub_obj_id;
    Asdu4Info.Addr.nZone = 0; // 默认区域
    
    // 设置信息对象
    bool bGetInfoObj = __DoGetFaultDistanceInfoObj(&eventField, Asdu4Info.InfoObj);
    if (!bGetInfoObj)
    {
        RcdErrLogWithParentClass("_CvtNxEventSubFieldToAsdu4Info():获取故障距离信息对象失败","TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 设置故障信息
    bool bGetFaultInfo = __ExtractFaultInfoFromField(&eventField, Asdu4Info.FaultInfo);
    if (!bGetFaultInfo)
    {
        RcdErrLogWithParentClass("_CvtNxEventSubFieldToAsdu4Info():提取故障信息失败","TNXEcProAsdu4GWS");
        return EC_PRO_CVT_FAIL;
    }
    
    // 设置时间信息
    Asdu4Info.InfoTime.nInfoHappenUtc = eventField.n_curvalueutctm;
    Asdu4Info.InfoTime.nInfoHappenMs = eventField.n_curms;
    Asdu4Info.InfoTime.nInfoRcvUtc = pMsg->n_send_utctm;
    Asdu4Info.InfoTime.nInfoRcvMs = 0; // 接收毫秒数，如果有的话
    Asdu4Info.InfoTime.nRetTime = 0; // 相对时间，需要根据实际需求设置
    
    // 设置品质和附加信息
    Asdu4Info.nQuality = 1; // 默认品质
    Asdu4Info.nSin = 0; // 附加信息
    
    sprintf(cError,"_CvtNxEventSubFieldToAsdu4Info():ASDU4信息结构转换成功,FUN=%d,INF=%d,故障距离=%.2f",
        Asdu4Info.InfoObj.nFun, Asdu4Info.InfoObj.nInf, Asdu4Info.FaultInfo.fScl);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu4GWS");
    
    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         获得故障距离事件信息体标识对象信息
* @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
* @param[out]    ASDU_INFO_OBJ & InfoObj:信息对象
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu4GWS::__DoGetFaultDistanceInfoObj(IN NX_EVENT_FIELD_STRUCT * pField, OUT ASDU_INFO_OBJ & InfoObj)
{
    char cError[255] = "";
    
    if (pField == NULL)
    {
        RcdErrLogWithParentClass("__DoGetFaultDistanceInfoObj():传入的事件子集结构为NULL","TNXEcProAsdu4GWS");
        return false;
    }
    
    // 根据实际需求设置功能类型和信息序号
    // 这里需要根据福建103的具体规约要求进行设置
    InfoObj.nFun = pField->n_sub_obj_id;        // 功能类型，可能需要映射
    InfoObj.nInf = pField->n_sub_sub_obj_id;    // 信息序号，可能需要映射
    InfoObj.nDpi = 0; // ASDU4中不使用DPI
    
    sprintf(cError,"__DoGetFaultDistanceInfoObj():故障距离信息对象生成成功,FUN=%d,INF=%d",
        InfoObj.nFun, InfoObj.nInf);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu4GWS");
    
    return true;
}

/**
* @brief         从事件字段中提取故障信息
* @param[in]     NX_EVENT_FIELD_STRUCT * pField：事件信息子集
* @param[out]    FAULT_INFO & FaultInfo:故障信息
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu4GWS::__ExtractFaultInfoFromField(IN NX_EVENT_FIELD_STRUCT * pField, OUT FAULT_INFO & FaultInfo)
{
    char cError[255] = "";
    
    if (pField == NULL)
    {
        RcdErrLogWithParentClass("__ExtractFaultInfoFromField():传入的事件子集结构为NULL","TNXEcProAsdu4GWS");
        return false;
    }
    
    // 提取故障距离
    // 这里需要根据实际的事件字段结构来提取故障距离信息
    // 可能需要从pField的某个字段或者通过数据库查询获取
    FaultInfo.fScl = 0.0; // 故障距离，需要从实际数据中获取
    FaultInfo.nFan = pField->n_value; // 故障序号，可能从事件值中获取
    
    // 如果有其他故障相关信息，也在这里提取
    
    sprintf(cError,"__ExtractFaultInfoFromField():故障信息提取成功,故障距离=%.2f,故障序号=%d",
        FaultInfo.fScl, FaultInfo.nFan);
    RcdTrcLogWithParentClass(cError,"TNXEcProAsdu4GWS");
    
    return true;
}
```

#### 2. 修改事件路由配置

在 `pro/nx_ec_pro_srv_fj103/NXEc60870CvtObj_GWS.cpp` 的 `ConvertEventMsgToPro()` 方法中添加故障事件的路由：

```cpp
switch(pMsg->n_msg_type)
{
    // ... 现有的case语句 ...
    
    // 【新增】故障距离事件路由到ASDU4
    // 方案1：复用现有事件类型
    case NX_IED_EVENT_EVENT_REPORT:  // 使用一般事件来处理故障测距
        // 需要在事件中通过特定字段来区分是否为故障测距事件
        pAsdu = new TNXEcProAsdu4GWS(m_pModelSeek,m_pLogRecord);
        break;
    
    // 方案2：新增专门的事件类型（需要先定义）
    // case NX_IED_EVENT_FAULT_DISTANCE_REPORT:  
    //     pAsdu = new TNXEcProAsdu4GWS(m_pModelSeek,m_pLogRecord);
    //     break;
        
    // ... 其他case语句 ...
}
```

#### 3. 更新编译配置

在 `pro/nx_ec_pro_srv_fj103/Makefile` 中添加新文件的编译规则。

## 可直接使用的现有代码资源

- ✅ **基类完整**: `TNXEcProAsdu4` 提供完整的基础功能
- ✅ **数据结构**: `ASDU2_4_12_INFO` 包含所有必要字段
- ✅ **报文格式化**: `FormatAsdu4Body()` 已实现完整的报文打包
- ✅ **时间处理**: 现有的时间转换机制可直接使用
- ✅ **地址处理**: 现有的地址获取机制可直接使用
- ✅ **日志系统**: 现有的日志记录机制可直接使用
- ✅ **继承模式**: 其他ASDU_GWS类提供了标准的继承实现模式

## 实现特点

1. **最小化实现**: 只需要实现福建103特有的事件处理逻辑
2. **继承复用**: 最大化利用基类已有功能
3. **模式一致**: 遵循工作区现有的ASDU_GWS实现模式
4. **扩展性好**: 便于后续添加更多故障相关功能

## 注意事项

1. **⚠️ 事件类型缺失**: 工作区中没有专门的故障测距事件定义，需要选择合适的处理方案
2. **故障信息获取**: 需要根据实际的事件字段结构调整故障信息提取逻辑
3. **功能类型映射**: 需要根据福建103规约确定FUN和INF的具体映射规则
4. **故障距离计算**: 可能需要实现具体的故障距离计算逻辑
5. **相对时间处理**: 需要根据实际需求设置相对时间字段

## 风险评估

- **低风险**: 基于成熟的基类和现有模式实现
- **兼容性**: 不影响现有功能，独立新增
- **测试需要**: 需要验证故障事件的正确处理和报文生成

## 实现优先级

1. **高优先级**: 实现基本的ASDU4事件处理和报文生成功能
2. **中优先级**: 完善故障信息的正确提取和映射
3. **低优先级**: 优化性能和错误处理机制

## 后续扩展

如果需要支持更多故障相关功能：
1. 支持多种故障类型的区分处理
2. 支持故障录波文件关联
3. 支持故障统计和分析功能
