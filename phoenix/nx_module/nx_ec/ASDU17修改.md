# 福建103 ASDU17 _CvtHisStrapChangeToPro 重载设计文档

## 1. 需求分析

### 1.1 业务需求
根据福建103规约要求，当主站召唤事件类型3（开关量变位信息）时，子站需要上送所有开关类信息：
- ✅ 开关量变位信息（已实现）
- ✅ 硬压板信息（已实现）  
- ❌ **软压板状态**（需要新增）
- ❌ **通信状态**（需要新增）

### 1.2 现状分析
- **基类实现**：`TNXEcProAsdu17::_CvtHisStrapChangeToPro()` 仅查询硬压板变位信息
- **福建103现状**：继承基类实现，缺少软压板和通信状态查询
- **数据库表结构**：
  - 软压板：`nx_t_ied_softstrap_data`
  - 通信状态：`nx_t_ied_commu_status`

## 2. 设计方案

### 2.1 总体架构
采用**最小侵入式重载**设计：
```cpp
// 福建103重载方法
int TNXEcProAsdu17FJ::_CvtHisStrapChangeToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList)
{
    // 1. 调用基类方法，获取硬压板变位信息
    TNXEcProAsdu17::_CvtHisStrapChangeToPro(mapAsduHistInfoList);
    
    // 2. 新增：查询软压板历史变位信息
    _CvtHisSoftStrapToPro(mapAsduHistInfoList);
    
    // 3. 新增：查询通信状态历史信息
    _CvtHisCommuStatusToPro(mapAsduHistInfoList);
    
    return 0;
}
```

### 2.2 核心设计原则
1. **完全兼容**：不修改基类，不影响其他协议实现
2. **复用现有架构**：遵循现有的数据库查询和数据转换模式
3. **事件类型标识**：确保输出数据能区分不同的事件子类型

## 3. 详细实现设计

### 3.1 新增方法1：软压板历史查询

#### 3.1.1 方法签名
```cpp
/**
* @brief        查询软压板历史变位信息，转换为协议信息
* @param[out]   MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList:历史信息列表
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisSoftStrapToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList);
```

#### 3.1.2 数据库查询设计
参考基类 `__GetHisHardStrapDataSet()` 实现：

```cpp
/**
* @brief        从数据库获取历史软压板数据
* @param[out]   CPlmRecordSet & RcdSet:数据集
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::__GetHisSoftStrapDataSet(OUT CPlmRecordSet & RcdSet);
```

**查询字段映射**：
| 字段名 | 用途 | 对应基类字段 |
|--------|------|-------------|
| strap_code | 软压板编号 | strap_code |
| curvalue | 当前值 | curvalue |
| curvaluetm | 变位时间 | curvaluetm |
| curvaluems | 变位时间毫秒 | curvaluems |
| ld_code | CPU编号 | ld_code |

**查询条件**：
- `ied_obj = m_nIedId` （设备ID）
- `curvaluetm >= 开始时间 AND curvaluetm <= 结束时间`
- `savereason = 0` （自动上送的变位数据）

### 3.2 新增方法2：通信状态历史查询

#### 3.2.1 方法签名
```cpp
/**
* @brief        查询通信状态历史信息，转换为协议信息
* @param[out]   MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList:历史信息列表  
* @return       0-执行成功，其他-执行失败
**/
int TNXEcProAsdu17FJ::_CvtHisCommuStatusToPro(OUT MAP_ASDU_HIS_INFO_LIST & mapAsduHistInfoList);
```

#### 3.2.2 数据库查询设计
```cpp
/**
* @brief        从数据库获取历史通信状态数据
* @param[out]   CPlmRecordSet & RcdSet:数据集
* @return       0-执行成功，其他-执行失败  
**/
int TNXEcProAsdu17FJ::__GetHisCommuStatusDataSet(OUT CPlmRecordSet & RcdSet);
```

**查询字段映射**：
| 字段名 | 用途 | 说明 |
|--------|------|------|
| cmmustat | 通信状态 | 1:正常 0:断开 2:未知 |
| curvaluetm | 状态变化时间 | 时间戳 |
| chgreason_obj | 变化原因 | 可选字段 |

**查询条件**：
- `ied_obj = m_nIedId` （设备ID）
- `curvaluetm >= 开始时间 AND curvaluetm <= 结束时间`

### 3.3 数据转换设计

#### 3.3.1 事件类型映射
为了在输出中区分不同的事件子类型，需要在 `__FormatAsduHisInfoToFramedata()` 中添加事件类型标识：

#### 3.3.2 ASDU_HIS_INFO 结构填充
参考基类 `_CvtHisStrapChangeToPro()` 的数据填充方式：

**软压板数据转换**：
```cpp
HisInfo.eType = EVENT_SOFTSTRAP;           // 事件类型：软压板变位
HisInfo.Addr.nAddr = m_AsduAddr.nAddr;     // 装置地址
HisInfo.Addr.nCpu = nCpuValue;             // CPU编号
HisInfo.InfoObj.nFun = pStrapTb->n_outfun103;  // fun值
HisInfo.InfoObj.nInf = pStrapTb->n_outinf103;  // inf值  
HisInfo.InfoObj.nDpi = curvalue + 1;       // dpi值
```

**通信状态数据转换**：
```cpp
HisInfo.eType = EVENT_SOFTSTRAP;        // 事件类型：复用一下软压板变位
HisInfo.Addr.nAddr = m_AsduAddr.nAddr;     // 装置地址
HisInfo.Addr.nCpu = 0;                     // 通信状态不分CPU
HisInfo.InfoObj.nFun = 0;                  // 根据配置确定
HisInfo.InfoObj.nInf = 0;                  // 根据配置确定
HisInfo.InfoObj.nDpi = cmmustat + 1;       // 通信状态值
```

## 4. 实现步骤

### 4.1 第一阶段：基础框架
1. 在 `NXEcProAsdu17_FJ.h` 中添加方法声明
2. 在 `NXEcProAsdu17_FJ.cpp` 中实现重载的 `_CvtHisStrapChangeToPro()`
3. 实现基础的数据库查询方法框架

### 4.2 第二阶段：软压板功能
1. 实现 `__GetHisSoftStrapDataSet()` 方法
2. 实现 `_CvtHisSoftStrapToPro()` 方法
3. 添加必要的配置表查询逻辑

### 4.3 第三阶段：通信状态功能  
1. 实现 `__GetHisCommuStatusDataSet()` 方法
2. 实现 `_CvtHisCommuStatusToPro()` 方法
3. 处理通信状态的特殊逻辑

