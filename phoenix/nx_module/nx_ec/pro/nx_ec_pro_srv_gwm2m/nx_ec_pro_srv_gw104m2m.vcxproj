﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5D2ED0D8-146C-473C-8C14-BFD2EB010526}</ProjectGuid>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <Keyword>ManagedCProj</Keyword>
    <RootNamespace>nx_ec_pro_srv_gw104m2m</RootNamespace>
    <ProjectName>nx_ec_pro_srv_gw104m2m</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CLRSupport>false</CLRSupport>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\debug;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/debug/nx_ec</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\release;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/release/nx_ec</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_USE_32BIT_TIME_T;_DEBUG;__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;_USE_32BIT_TIME_T;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT;</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_mb.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecord.h" />
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.h" />
    <ClInclude Include="..\..\..\..\..\nx_common\nx_errno_def.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\FileOperate.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\LogRecord.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\MyDeque.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_def.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\plm_dbm\DataRow.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\ThreadMutualLock.h" />
    <ClInclude Include="..\..\..\..\..\platform_include\TimeConvert.h" />
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h" />
    <ClInclude Include="..\..\ec_common\NXECObject.h" />
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h" />
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu1.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu101.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu102.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu103.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu12.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu13.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu15.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu16.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu17.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu18.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu2.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu4.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu42.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu6.h" />
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu7.h" />
    <ClInclude Include="NXEc60870CvtFactoryGWM2M.h" />
    <ClInclude Include="NXEc60870CvtObjGWM2M.h" />
    <ClInclude Include="NXEcGWM2M104SrvProtocol.h" />
    <ClInclude Include="NXEcIec104ExplainFactoryGWM2M.h" />
    <ClInclude Include="NXEcIec104ProExplainGWM2M.h" />
    <ClInclude Include="NXEcProAsdu10GWM2M.h" />
    <ClInclude Include="NXEcProAsdu12GWM2M.h" />
    <ClInclude Include="NXEcProAsdu13GWM2M.h" />
    <ClInclude Include="NXEcProAsdu15GWM2M.h" />
    <ClInclude Include="NXEcProAsdu16GWM2M.h" />
    <ClInclude Include="NXEcProAsdu17GWM2M.h" />
    <ClInclude Include="NXEcProAsdu1GWM2M.h" />
    <ClInclude Include="NXEcProAsdu21GWM2M.h" />
    <ClInclude Include="NXEcProAsdu7GWM2M.h" />
    <ClInclude Include="NXEcProAsduGWM2M.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp" />
    <ClCompile Include="ec_pro_srv_gw104m2m_export.cpp" />
    <ClCompile Include="NXEc60870CvtFactoryGWM2M.cpp" />
    <ClCompile Include="NXEc60870CvtObjGWM2M.cpp" />
    <ClCompile Include="NXEcGWM2M104SrvProtocol.cpp" />
    <ClCompile Include="NXEcIec104ExplainFactoryGWM2M.cpp" />
    <ClCompile Include="NXEcIec104ProExplainGWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu10GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu12GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu13GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu15GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu16GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu17GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu1GWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu21_DirectGWM2M.cpp" />
    <ClCompile Include="NXEcProAsdu7GWM2M.cpp" />
    <ClCompile Include="NXEcProAsduGWM2M.cpp" />
    <ClCompile Include="nx_ec_pro_srv_gw104m2m_modify_note.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_gw104m2m.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>