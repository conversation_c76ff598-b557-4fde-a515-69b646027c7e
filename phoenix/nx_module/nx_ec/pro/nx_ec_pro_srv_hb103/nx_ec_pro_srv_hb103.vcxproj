﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{56279A14-8A9E-4B87-935D-E76BF322BD19}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>nx_ec_pro_srv_hb103</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\debug;$(LibraryPath)</LibraryPath>
    <OutDir>../../../../../nx_bin/debug/nx_ec</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/</IncludePath>
    <LibraryPath>$(VCInstallDir)lib;$(VCInstallDir)atlmfc\lib;$(WindowsSdkDir)lib;$(FrameworkSDKDir)\lib;..\..\..\..\..\nx_lib\release</LibraryPath>
    <OutDir>../../../../../nx_bin/release/nx_ec</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;NX_EC_PROTOCOL_EXPORT;__PLATFORM_MS_WIN__;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;NX_EC_PROTOCOL_EXPORT;__PLATFORM_MS_WIN__;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="NXEc60870CvtFactory_HB.h" />
    <ClInclude Include="NXEc60870CvtObj_HB.h" />
    <ClInclude Include="NXEcHB103SrvProtocol.h" />
    <ClInclude Include="NXEcIec103ExplainFactory_HB.h" />
    <ClInclude Include="NXEcIec103ProExplain_HB.h" />
    <ClInclude Include="NXEcProAsdu101_HB.h" />
    <ClInclude Include="NXEcProAsdu103_HB.h" />
    <ClInclude Include="NXEcProAsdu10_HB.h" />
    <ClInclude Include="NXEcProAsdu12_HB.h" />
    <ClInclude Include="NXEcProAsdu13_HB.h" />
    <ClInclude Include="NXEcProAsdu15_HB.h" />
    <ClInclude Include="NXEcProAsdu16_HB.h" />
    <ClInclude Include="NXEcProAsdu17_HB.h" />
    <ClInclude Include="NXEcProAsdu1_HB.h" />
    <ClInclude Include="NXEcProAsdu21_HB.h" />
    <ClInclude Include="NXEcProAsdu2_HB.h" />
    <ClInclude Include="NXEcProAsdu42_HB.h" />
    <ClInclude Include="NXEcProAsdu4_HB.h" />
    <ClInclude Include="NXEcProAsdu7_HB.h" />
    <ClInclude Include="resource.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp" />
    <ClCompile Include="ec_pro_srv_hb103_export.cpp" />
    <ClCompile Include="NXEc60870CvtFactory_HB.cpp" />
    <ClCompile Include="NXEc60870CvtObj_HB.cpp" />
    <ClCompile Include="NXEcHB103SrvProtocol.cpp" />
    <ClCompile Include="NXEcIec103ExplainFactory_HB.cpp" />
    <ClCompile Include="NXEcIec103ProExplain_HB.cpp" />
    <ClCompile Include="NXEcProAsdu101_HB.cpp" />
    <ClCompile Include="NXEcProAsdu103_HB.cpp" />
    <ClCompile Include="NXEcProAsdu10_HB.cpp" />
    <ClCompile Include="NXEcProAsdu12_HB.cpp" />
    <ClCompile Include="NXEcProAsdu13_HB.cpp" />
    <ClCompile Include="NXEcProAsdu15_HB.cpp" />
    <ClCompile Include="NXEcProAsdu16_HB.cpp" />
    <ClCompile Include="NXEcProAsdu17_HB.cpp" />
    <ClCompile Include="NXEcProAsdu1_HB.cpp" />
    <ClCompile Include="NXEcProAsdu21_Direct_HB.cpp" />
    <ClCompile Include="NXEcProAsdu21_HB.cpp" />
    <ClCompile Include="NXEcProAsdu2_HB.cpp" />
    <ClCompile Include="NXEcProAsdu42_HB.cpp" />
    <ClCompile Include="NXEcProAsdu4_HB.cpp" />
    <ClCompile Include="NXEcProAsdu7_HB.cpp" />
    <ClCompile Include="nx_ec_pro_srv_hb103_modify_note.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_hb103.rc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="ecpro.ini" />
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>