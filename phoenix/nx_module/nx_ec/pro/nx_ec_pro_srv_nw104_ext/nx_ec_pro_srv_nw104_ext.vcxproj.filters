﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="ec_pro_srv_nw104_ext_export.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXT60870CvtFactory.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXT60870CvtObj.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTSrvProtocol.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu12.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu13.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu16.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu17.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu2.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu20.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu21_Direct.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu7.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\ec_pro_60870_modify_note.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\ec_pro_common_modify_note.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp">
      <Filter>EC_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp">
      <Filter>EC_PRO_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu101.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu103.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>COMMON</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>EC_COMMON</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu15.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu1.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu105.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu107.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcFileCommon.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="nx_ec_pro_srv_nw104_ext_modify_note.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
    <ClCompile Include="NXEcNW104EXTProAsdu10.cpp">
      <Filter>NW104EXT</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXEcNW104EXT60870CvtFactory.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTSrvProtocol.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXT60870CvtObj.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu12.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu13.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu16.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu17.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu2.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu20.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu21.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu7.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec104ProExplain.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu1.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu10.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu101.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu102.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu103.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu12.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu13.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu15.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu16.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu17.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu18.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu2.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu21.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu4.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu42.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu6.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcProAsdu7.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcProtocol.h">
      <Filter>EC_PRO_COMMON_IEC60870</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\EcProCommonFun.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXLoadEcModelLib.h">
      <Filter>EC_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcSrvProtocol.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadProOperationLib.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoadMsgOperationLib.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcLoad60870FlowLib.h">
      <Filter>EC_PRO_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu101.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu103.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.h">
      <Filter>COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecord.h">
      <Filter>COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXECObject.h">
      <Filter>EC_COMMON</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\os_platform_fun.h">
      <Filter>COMMON</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu15.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu1.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu105.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu107.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcFileCommon.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
    <ClInclude Include="NXEcNW104EXTProAsdu10.h">
      <Filter>NW104EXT</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="NW104EXT">
      <UniqueIdentifier>{a899787d-f783-450d-8a93-dd61040ab6f4}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC_COMMON">
      <UniqueIdentifier>{16b56f35-8cfa-46da-a372-585bb1b2d3e3}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC_PRO_COMMON">
      <UniqueIdentifier>{b4b2fc4b-4260-47a6-b306-0516c0d70b48}</UniqueIdentifier>
    </Filter>
    <Filter Include="EC_PRO_COMMON_IEC60870">
      <UniqueIdentifier>{50c400ec-1a18-43ad-a05c-542daed9e75c}</UniqueIdentifier>
    </Filter>
    <Filter Include="COMMON">
      <UniqueIdentifier>{598102ea-df09-4b98-9656-315ec276c517}</UniqueIdentifier>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{c965e787-1b19-4949-b330-7e34f7b47533}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_srv_nw104_ext.rc">
      <Filter>NW104EXT</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>