﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="MakeFile">
      <UniqueIdentifier>{df99e6e1-8211-4c4a-84ec-270e5f463ee5}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\ec_common\ec_common_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\CommuTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\platform_include\SYCommunDllPack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\ec_pro_common_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\ec_pro_commuflow_103_def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\INXEcExplainFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\INXEcProExplain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\NXEcProTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\EcTemplateFunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\ec_common\NXECObject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc103ProTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc103ProSrvTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NXEc103ProCliTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ec_pro_common\INXEcProTransObj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ec_pro_common\NXEcProTransObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\ec_common\NXECObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc103ProTransObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc103ProCliTransObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NXEc103ProSrvTransObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_pro_commuflow_103_export.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec_103flow_modify_note.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="nx_ec_pro_commuflow_103.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>MakeFile</Filter>
    </None>
  </ItemGroup>
</Project>