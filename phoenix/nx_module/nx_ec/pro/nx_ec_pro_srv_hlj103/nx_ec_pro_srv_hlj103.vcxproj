﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp" />
    <ClCompile Include="..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.cpp" />
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinystr.cpp" />
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxml.cpp" />
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="..\..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp" />
    <ClCompile Include="..\..\ec_common\EcTemplateFunc.cpp" />
    <ClCompile Include="..\..\ec_common\NXECObject.cpp" />
    <ClCompile Include="..\..\ec_common\NXLoadEcModelLib.cpp" />
    <ClCompile Include="..\ec_pro_common\EcProCommonFun.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoad60870FlowLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadMsgOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcLoadProOperationLib.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common\NXEcSrvProtocol.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ExplainFactory.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcIec103ProExplain.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu1.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu10.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu101.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu102.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu103.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu12.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu13.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu15.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu16.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu17.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu18.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu2.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu4.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu42.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu6.cpp" />
    <ClCompile Include="..\ec_pro_common_iec60870\NXEcProAsdu7.cpp" />
    <ClCompile Include="ec_pro_srv_hlj103_export.cpp" />
    <ClCompile Include="ec_srv_hlj103_modify_note.cpp" />
    <ClCompile Include="NXEc60870CvtFactory_HLJ.cpp" />
    <ClCompile Include="NXEc60870CvtObj_HLJ.cpp" />
    <ClCompile Include="NXEcHLJ103SrvProtocol.cpp" />
    <ClCompile Include="NXEcIec103ExplainFactory_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu103_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu10_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu12_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu13_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu15_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu16_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu1_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu2_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu21_Direct_HLJ.cpp" />
    <ClCompile Include="NXEcProAsdu7_HLJ.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="NXEc60870CvtFactory_HLJ.h" />
    <ClInclude Include="NXEc60870CvtObj_HLJ.h" />
    <ClInclude Include="NXEcHLJ103SrvProtocol.h" />
    <ClInclude Include="NXEcIec103ExplainFactory_HLJ.h" />
    <ClInclude Include="NXEcProAsdu103_HLJ.h" />
    <ClInclude Include="NXEcProAsdu10_HLJ.h" />
    <ClInclude Include="NXEcProAsdu12_HLJ.h" />
    <ClInclude Include="NXEcProAsdu13_HLJ.h" />
    <ClInclude Include="NXEcProAsdu15_HLJ.h" />
    <ClInclude Include="NXEcProAsdu16_HLJ.h" />
    <ClInclude Include="NXEcProAsdu1_HLJ.h" />
    <ClInclude Include="NXEcProAsdu21_HLJ.h" />
    <ClInclude Include="NXEcProAsdu2_HLJ.h" />
    <ClInclude Include="NXEcProAsdu7_HLJ.h" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8C6FB9F4-1594-4122-9F94-349E93174F0C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>nx_ec_pro_srv_hlj103</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>../../../../../nx_bin/debug/nx_ec</OutDir>
    <TargetExt>.dll</TargetExt>
    <IncludePath>../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\debug;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>../../../../../nx_bin/release/nx_ec</OutDir>
    <IncludePath>$(VCInstallDir)include;$(VCInstallDir)atlmfc\include;$(WindowsSdkDir)include;$(FrameworkSDKDir)\include;../../../../../nx_common/;../../../../../platform_include/plm_common/;../../../../../platform_include/plm_commun/;../../../../../platform_include/plm_dbm/;../../ec_common/;../ec_pro_common/;../ec_pro_common_iec60870/;E:\SVN20171106\phoenix\trunk\thirdparty\tinyxml</IncludePath>
    <LibraryPath>..\..\..\..\..\nx_lib\release;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;NX_EC_PROTOCOL_EXPORT;__PLATFORM_MS_WIN__;_USE_32BIT_TIME_T;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>sylib.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_USE_32BIT_TIME_T;NDEBUG;%(PreprocessorDefinitions);__PLATFORM_MS_WIN__;_CRT_SECURE_NO_WARNINGS;NX_EC_PROTOCOL_EXPORT;</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>NotSet</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>
      </EnableCOMDATFolding>
      <OptimizeReferences>
      </OptimizeReferences>
      <AdditionalDependencies>sylib.lib;plm_mb.lib;plm_db.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>