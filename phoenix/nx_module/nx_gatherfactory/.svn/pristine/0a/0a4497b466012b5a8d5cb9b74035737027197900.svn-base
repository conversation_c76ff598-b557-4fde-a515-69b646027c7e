#pragma once

#include "IED.h"

#include "Load_dbm_lib.h"
#include "IPlm_DBManager.h"

#define RET_FAIL  1
#define RET_SUCCESS 0

typedef struct _STATION_DEF
{
	int nStationID;
	int nAreaID;
	_STATION_DEF()
	{
		nStationID = -1;
		nAreaID = -1;
	}
}STATION_DEF;

typedef struct _IED_DEF
{
	int nIedID;
	int nStationID;
	int nPrimequID;
	_IED_DEF()
	{
		nIedID = -1;
		nStationID = -1;
		nPrimequID = -1;
	}
}IED_DEF;

typedef struct _ANALOG_DEF
{
	int nIedID;
	int nAnalogID;
	int nItemID;
	string strCurVal;
	_ANALOG_DEF()
	{
		nIedID = -1;
		nAnalogID = -1;
		strCurVal = "";
	}
}ANALOG_DEF;

class CCoIED: public CIED
{
public:
	CCoIED(FACTORY_ATBUTE*pFactory, IED_ATBUTE* pIED, CLogRecord* pLog);
	virtual ~CCoIED(void);
public:
	BOOL InitIED();
	BOOL CallAI(int nld);
	BOOL CallState(int nld);
	BOOL Relogin();
private:
	int OpenDBConn();
	bool Select_records( DB_OPER_PARAM& st_db_oper_param, CPlmRecordSet &RcdSet, UINT& u_Row_num,char* c_errorinfo = NULL);
	bool Select_Analog_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);
	bool Select_Digit_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);
	bool Select_Commu_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);
	inline void AddACondition( DB_OPER_PARAM& st_db_oper_param, string cdt_name, string cdt_value, TCDT_TYPE cdt_type = CDT_TYPE_EQUAL, TCDT_LOGICAL cdt_logical = CDT_LOGIAL_AND );
	inline void LstFiledData_push_back(DB_OPER_PARAM& st_db_oper_param, DB_FIELD_DATA& st_db_filed_data, string str_fdname);
	template<typename T>
	string Int2Str(T in_T);
	template<typename T>
	void GetAFiledValue(CPlmRecordSet& RecordSet, UINT& FieldNum, T& T_Input);
	void GetAFiledValue(CPlmRecordSet& RecordSet, UINT& FieldNum, float& f_input);
	void GetAFiledValue(CPlmRecordSet& RecordSet, UINT& FieldNum, char* input_c_str, int nSize);

	bool Select_Area_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);
	bool Select_Station_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);
	bool Select_Ied_Info( CPlmRecordSet& RcdSet, UINT& u_Row_num);

	void InitAreaInfo();
	void InitStationInfo();
	void InitIedInfo();

	BOOL CheckIedCommuStatus(int nIed);
	BOOL CheckIedAlarmStatus(int nIed);
	BOOL CheckIedFaultStatus(int nIed);

	void DealCommuStatusValue(int nld, vector<AI_ATBUTE>& vAnalogValue);
	void DealAlarmStatusValue(int nld, vector<AI_ATBUTE>& vAnalogValue);
	void DealFaultStatusValue(int nld, vector<AI_ATBUTE>& vAnalogValue);

	void DealIedCommuStatusValue(int nld, int nIed, BOOL bIedCommu, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealIedAlarmStatusValue(int nld, int nIed, BOOL bCoolerAlarm, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealIedFaultStatusValue(int nld, int nIed, BOOL bCoolerFault, vector<AI_ATBUTE>& vSendAnalogValue);

	void DealStationCommuStatusValue(int nld, int nStation, BOOL bStationCommu, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealStationAlarmStatusValue(int nld, int nStation, BOOL bStationAlarm, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealStationFaultStatusValue(int nld, int nStation, BOOL bStationFault, vector<AI_ATBUTE>& vSendAnalogValue);

	void DealAreaCommuStatusValue(int nld, int nArea, BOOL bAreaCommuFault, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealAreaAlarmStatusValue(int nld, int nArea, BOOL bAreaAlarm, vector<AI_ATBUTE>& vSendAnalogValue);
	void DealAreaFaultStatusValue(int nld, int nArea, BOOL bAreaFault, vector<AI_ATBUTE>& vSendAnalogValue);

	void CheckFaultStatus();
	void CheckAlarmStatus();
	void CheckCommuStatus();
private:
	CLoad_dbm_lib m_load_dbm_lib;
	IPlm_DBManager* m_pDBM;
	vector<int> m_vAreaList;
	vector<STATION_DEF> m_vStationList;
	vector<IED_DEF> m_vIedList;
	map<int, vector<ANALOG_DEF> > m_mAnalogListMap;
};


