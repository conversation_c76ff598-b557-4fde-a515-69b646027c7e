#include "CoDirect.h"

#ifdef __cplusplus
extern "C"
{
#endif
	MY_DLL_EXPORT BOOL start_ieds_run(MODEL_RUN_PARAM* pModleRunParam);

#ifdef __cplusplus
}
#endif

BOOL start_ieds_run(MODEL_RUN_PARAM* pModleRunParam)
{
	try
	{
		BOOL bResult = FALSE;

		if (pModleRunParam == NULL)
		{
			return FALSE;
		}

		CCoDirect* pDirect = new CCoDirect();
		if (pDirect == NULL)
		{
			return FALSE;
		}

		bResult = pDirect->StartRun(pModleRunParam);

		return bResult;
	}
	catch(...)
	{
		return FALSE;
	}
}
