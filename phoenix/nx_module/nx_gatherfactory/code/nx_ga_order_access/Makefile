#define the path of search
VPATH = -I./ \
				-I../nx_ga_common \
				-I../../../../nx_common \
				-I../../../../platform_include/plm_common \
				-I../../../../platform_include/plm_commun
LABLE = 1.1.29.6

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OBJ_PATH = ./release/
	OUT_PATH = ../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../nx_lib/release/
	OBJ_NAME = nx_ga_order_access.so-$(LABLE)
else
	RUN_FLAGS = -g
	OBJ_PATH = ./debug
	OUT_PATH = ../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../nx_lib/debug/
	OBJ_NAME = nx_ga_order_access.so
endif

GA_TYPE=Z2000_GA
ifeq ($(Z2800_GA),Y)
	GA_TYPE=Z2800_GA
endif

LIBS =  -L./ -L$(LIB_PATH) -lsylib -lpthread -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__ -D$(GA_TYPE)

#object file definition
OBJS = $(OBJ_PATH)IedOrderManager.o $(OBJ_PATH)IOrderManager.o $(OBJ_PATH)OrderManager.o $(OBJ_PATH)NxmbAppMngr.o $(OBJ_PATH)ga_datatoorder.o   

nx_ga_order_access.so : $(OBJS) mklibdir
	g++ -o $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

$(OBJ_PATH)IedOrderManager.o : ./IedOrderManager.cpp mkobjdir
	g++ -c ./IedOrderManager.cpp -o $(OBJ_PATH)IedOrderManager.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)IOrderManager.o : ./IOrderManager.cpp
	g++ -c ./IOrderManager.cpp -o $(OBJ_PATH)IOrderManager.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)OrderManager.o : ./OrderManager.cpp
	g++ -c ./OrderManager.cpp -o $(OBJ_PATH)OrderManager.o $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)ga_datatoorder.o : ./ga_datatoorder.cpp
	g++ -c ./ga_datatoorder.cpp -o $(OBJ_PATH)ga_datatoorder.o $(CFLAGS) $(VPATH)

$(OBJ_PATH)NxmbAppMngr.o : ../../../../nx_common/NxmbAppMngr.cpp
	g++ -c ../../../../nx_common/NxmbAppMngr.cpp -o $(OBJ_PATH)NxmbAppMngr.o $(CFLAGS) $(VPATH)

mklibdir : 
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi

mkobjdir : 
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
