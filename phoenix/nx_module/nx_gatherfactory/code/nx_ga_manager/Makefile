#define the path of search
VPATH	=	-I../../../../platform_include/plm_common	\
			-I../../../../platform_include/plm_commun \
				-I../../../../nx_common					\
				-I../nx_ga_common								\
				-I./
LABLE = 1.0.10

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OBJ_PATH = ../../../../nx_bin/release/ga/
	OBJ_NAME = nx_ga_manager-$(LABLE)
	LIB_PATH = ../../../../nx_lib/release/
	PLATFORM_LIB_PATH  = -L../../../../platform_lib/release/
else
	RUN_FLAGS = -g
	OBJ_PATH = ../../../../nx_bin/debug/ga/
	OBJ_NAME = nx_ga_manager
	LIB_PATH = ../../../../nx_lib/debug/
	PLATFORM_LIB_PATH  = -L../../../../platform_lib/debug/
endif

GA_TYPE=Z2000_GA
ifeq ($(Z2800_GA),Y)
	GA_TYPE=Z2800_GA
endif

LIBS =  -L./ -L$(LIB_PATH) -rdynamic -lpthread -ldl -lnsl -lsylib -lrt $(PLATFORM_LIB_PATH)

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__ -D$(GA_TYPE)

#object file definition
OBJS = GaDeal.o GaManger.o ServerRun.o


nx_ga_manger : $(OBJS) mksylibdir cpini
	g++ -o  $(OBJ_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS)

GaDeal.o : ./GaDeal.cpp
	g++ -c ./GaDeal.cpp $(CFLAGS) $(VPATH)

GaManger.o : ./GaManger.cpp
	g++ -c ./GaManger.cpp $(CFLAGS) $(VPATH)
	
ServerRun.o : ./ServerRun.cpp
	g++ -c ./ServerRun.cpp $(CFLAGS) $(VPATH)

mksylibdir : 
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
cpini:
	cp -fp ./*.ini $(OBJ_PATH).
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OBJ_PATH)$(OBJ_NAME) $(OBJS)
