﻿/****************************************************************
*  CoIED.h       author: gcy      date: 11/01/2016
*----------------------------------------------------------------
*  note: 实现浪拜迪录波器采集规约类
*    注意：本规约的接收报文缓冲区m_read_buff变量里只有报文的报文头，
	 因为报文太长，超出m_read_buff大小，真实报文是放在m_CurTransFile.pCurWholePacket中。
*****************************************************************/

#ifndef _COIED_H_
#define _COIED_H_

#include <bitset>
#include "IED.h"
#include "ga_dfrlist.h"
#include "LBD_define.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"

/** @brief   CPU属性迭代器*/
typedef vector<LD_ATBUTE>::iterator it_Ld;

/**
*@defgroup   浪拜迪录波器采集规约类
*@{
*/

/**
*@brief      实现浪拜迪录波器数据采集
*<AUTHOR>
date:        19/01/2016
*
*example
*@code*
*    
*    
*    
*    
@endcode
*/
#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief			自动切换状态
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	void AutoSwitchState();

	/**
	* @brief			召唤指定LD的通讯状态
	* @param[in]		nld,LD编号
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallState(int nld);

	/**
	* @brief			召唤指定LD的定值
	* @param[in]		nld,LD编号
	* @param[in]		zone,定值区号: -1:表示召唤当前区定值
	* @return			TRUE：成功， FALSE：失败                       
	*/
	BOOL CallSG(int nld, int zone);

	/**
	* @brief			召唤指定LD的文件列表(当pStart_time = NULL &&  pEnd_time = NULL, 表示召唤当前最新文件列表 )
	* @param[in]		nld	LD编号
	* @param[in]		pStart_time	起始时间   
	* @param[in]		pEnd_time	结束时间
	* @return			TRUE：成功， FALSE：失败		           
	*/
	BOOL CallFileList(int nld, MY_TIME_INFO* pStart_time, MY_TIME_INFO* pEnd_time);

	/**
	* @brief			召唤指定的波形文件
	* @param[in]		filename 需要召唤的文件名称
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallFile(string& filename);

	/**
	* @brief     	    召唤所有新文件
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallAllFile();

	/**
	* @brief     	远方触发录波
	* @param[in]    需要触发的LD
	* @return       TRUE：成功， FALSE：失败
	*/
	BOOL TriggerOsc(int nld);

	/**
	* @brief		对时
	* @return		TRUE：成功， FALSE：失败                    
	*/
	BOOL UpdateTime();

	/**
	* @brief	    信号复位
	* @param[in]    nld 需要复位的LD
	* @return		TRUE：成功， FALSE：失败                       
	*/
	BOOL Reset(int nld);

	/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();

private:
	/** @brief          当前cpu*/
	u_int8 m_nCurCpu;

	/** @brief			召唤录波方式   */
	u_int8 m_u_CallFileType;

	/** @brief			本地录波存放目录（绝对路径）*/
	string m_strWavePath;

	/** @brief			上次发送心跳报文的时标*/
	unsigned long m_nLastTestTime;

	/** @brief			录波文件列表处理类对象*/
	CDFRList* m_pCurDfrList;

	/** @brief			当前正在传输的文件信息*/
	FILE_TRANS_INFO m_CurTransFile;

	/** @brief			当前文件列表*/
	vector<DFR_FILE_INF> m_vCurFileList;

	/** @brief			待召唤文件列表*/
	vector<DFR_FILE_INF> m_vCallFileList;

	/** @brief          存储在本地的定值全路径*/
	string m_strLocalSGFile;

	/** @brief	录波定值结构 */
	LBQ	m_LbdSettings;

private:
	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	/**
	* @brief     	初始化装置通信状态
	* @param[in]    无
	* @param[out]   无
	* @return       void
	*/
	void InitIedState();

	/**
	* @brief     	打开通讯端口
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL OpenCommun();

	/**
	* @brief     	创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]   pDllPack 通讯库包装对象
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief     	从已收到的报文头中得出接下来需要接收的报文长度和该报文类型
	* @param[out]   nMsgType 报文类型
	* @return       int  要接入的报文长度
	*/
	int GetRemainLenFromPacket(int& nMsgType);

	/**
	* @brief     	打包召唤录波列表报文
	* @param[in]    无
	* @param[out]   无
	* @return       void
	*/
	void PackCallFileList();

	/**
	* @brief     	打包召唤录波文件报文
	* @param[out]   gcFileName 录波文件名，不带路径
	* @return       void
	*/
	void PackCallFile(char* gcFileName);

	/**
	* @brief     	收发报文操作
	* @param[in]    Type 操作类型，收还是发，亦或两者都有
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DataReadWriteProcess(int Type);

	/**
	* @brief     	解析录波文件列表
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DistillFileList();

	/**
	* @brief     	发送心跳报文
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL SendHeartBeatMsg();

	/**
	* @brief		解析单个录波的报文，并生成目标文件
	* @param[in]	strFileName		接收的数据文件名称
	* @return		-2:异常失败  -1: 解析成功  >=0:当前临时文件的长度
	* @note							          
	*/
	int ExtractTmpFile(string strFileName);

	/**
	* @brief     	    组装报文头
	* @param[in]        dwMsgType,报文类型
	* @param[in]        
	* @return           void
	*/
	void PackMsgHead( int nMsgType );

	/**
	* @brief     	收发报文
	* @param[in]    无
	* @param[out]   无
	* @return       int 0成功 -1发送失败 -2 接收失败
	*/
	int SendRecvData(int mode = READ_AND_WRITE);

	/**
	* @brief     		 发送报文
	* @param[in]     	 无
	* @param[out]    	 无
	* @return          	 int,0 成功 1失败
	*/
	int SendData();

	/**
	* @brief     	    从socket接收数据
	* @param[in]        无
	* @param[out]       无
	* @return           int，0成功 －1失败， －2 超时
	*/
	int RecvData();

	/**
	* @brief     	   初始化发送缓冲区
	* @param[in]       无
	* @param[out]      无
	* @return          void
	*/
	void InitSendMsgBuff();

	/**
	* @brief     	处理录波文件中的一个文件
	* @param[in]    strFilename 文件名，带扩展名
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DistillSingleOscFile(string strFilename);
		
	/**
	* @brief     	召唤定值报文
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL RequestSettingsMsg();

	/**
	* @brief     	   从装置取最新文件列表
	* @param[in]	   无
	* @param[out]	   无
	* @return		   BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallFileListFromDevice();

	/**
	* @brief     	 召唤一个COMTRADE文件（包含hdr\cfg\dat）
	* @param[in]     strFileName，文件名,不带路径和扩展名
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallComtradeFile(string strFileName);

	/**
	* @brief     	解析定值数据
	* @param[in]    
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL DistillSettings();

	/**
	* @brief     	查找并处理一个定值数据
	* @param[in]    nCpuNo CPU号
	* @param[in]    nId    定值ID
	* @param[in]    strValue 定值的值
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CheckSgItem(int nCpuNo, int nId, string strValue);

	/**
	* @brief     	将列表中字符串时间转换到MY_TIME_INFO格式
	* @param[in]    pStrTime     字符串时间
	* @param[out]   stFaultTime  MY_TIME_INFO格式时间
	* @return       void
	*/
	void ConvertStrTimeToFaultTime(char* pStrTime, MY_TIME_INFO& stFaultTime);

	/**
	* @brief     	将收到一帧完整的报文（一个文件的内容）写到临时文件中
	* @param[out]   pTempBuff     一帧完整的报文
	* @param[in]    nPacketSize   报文长度
	* @return       void
	*/
	void WriteDebugTempFile(u_int8* pTempBuff, int nPacketSize);
};
/** @} */ //CCoIED OVER

#endif