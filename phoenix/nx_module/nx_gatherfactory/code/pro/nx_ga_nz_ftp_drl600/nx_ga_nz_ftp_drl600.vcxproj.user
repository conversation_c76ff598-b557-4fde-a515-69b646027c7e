﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerCommand>D:\SVN\Phoenix\trunk\nx_bin\debug\ga\nx_ga_factory.exe</LocalDebuggerCommand>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerCommandArguments>1</LocalDebuggerCommandArguments>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LocalDebuggerWorkingDirectory>D:\SVN\Phoenix\trunk\nx_bin\debug\ga\</LocalDebuggerWorkingDirectory>
    <DebuggerFlavor>WindowsLocalDebugger</DebuggerFlavor>
    <RemoteDebuggerCommand>D:\ZXIN\Z2000\nx_bin\ga\nx_ga_factory.exe</RemoteDebuggerCommand>
    <RemoteDebuggerServerName>7.176.31.225</RemoteDebuggerServerName>
    <RemoteDebuggerConnection>RemoteWithoutAuthentication</RemoteDebuggerConnection>
    <RemoteDebuggerCommandArguments>1</RemoteDebuggerCommandArguments>
    <RemoteDebuggerWorkingDirectory>D:\ZXIN\Z2000\nx_bin\ga\</RemoteDebuggerWorkingDirectory>
    <RemoteDebuggerAttach>false</RemoteDebuggerAttach>
  </PropertyGroup>
</Project>