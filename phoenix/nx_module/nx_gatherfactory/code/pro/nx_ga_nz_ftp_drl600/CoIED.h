﻿/****************************************************************
*  CoIED.h       author: gcy      date: 05/09/2013
*----------------------------------------------------------------
*  note: 实现南自DRL-600录波器采集规约类
*  
*****************************************************************/

#pragma once

#include "BaseFTP.h"

/** @brief   CPU属性迭代器*/
typedef vector<LD_ATBUTE>::iterator it_Ld;

/**
*@defgroup   许继电气WGL600录波器采集规约类
*@{
*/

/**
*@brief      
*<AUTHOR>
date:        30/08/2014
*
*example
*@code*
*    
*    
*    
*    
@endcode
*/
#pragma once
class CCoIED : public CBaseFtpIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);
};
/** @} */ //CCoIED OVER