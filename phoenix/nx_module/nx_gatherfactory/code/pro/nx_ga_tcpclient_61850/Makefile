#define the path of search
VPATH = -I./ \
				-I../../nx_ga_common \
				-I../../../../../nx_common \
				-I../../../../../platform_include/plm_common \
				-I../../../../../platform_include/plm_commun \
				-I../../../../../platform_include/external/cosmos/inc \
				-I../../../../../platform_include/external/cosmos/mmslite/inc \
				-I../../../../../platform_include/external/

LABLE = 1.0.82

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OBJ_PATH = ./release/
	OUT_PATH = ../../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../../nx_lib/release/
	PLATFORM_LIB_PATH  = -L../../../../../platform_lib/release/
	COSMOS_PATH = ../../../../../platform_include/external/cosmos/lib/linux/
	COSMOS_LIB = cosmos_l
	OBJ_NAME = nx_ga_tcpclient_61850.so-$(LABLE)
else
	RUN_FLAGS = -g
	OBJ_PATH = ./debug/
	OUT_PATH = ../../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../../nx_lib/debug/
	PLATFORM_LIB_PATH  = -L../../../../../platform_lib/debug/
	COSMOS_PATH = ../../../../../platform_include/external/cosmos/lib/linux/
	COSMOS_LIB = cosmos_ld
	OBJ_NAME = nx_ga_tcpclient_61850.so-$(LABLE)_debug
endif
PRJ_COMMON = $(shell ls ../../../../../ | grep -e nx_common -e uncommon)

#GA_TYPE=Z2000_GA
#ifeq ($PRJ_COMMON),nx_common)
#	GA_TYPE=Z2000_GA
#endif

#ifeq ($PRJ_COMMON),uncommon)
#	GA_TYPE=Z2800_GA
#endif

GA_TYPE=Z2000_GA
ifeq ($(Z2800_GA),Y)
	GA_TYPE=Z2800_GA
endif

LIBS =  -L./ -L$(OUT_PATH) -lpthread -L$(LIB_PATH) -lsylib -L$(COSMOS_PATH) -l$(COSMOS_LIB) -ldl -rdynamic -lrt $(PLATFORM_LIB_PATH) -lxerces-c

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__ -DMMS_LITE -DS_MT_SUPPORT -D$(GA_TYPE)

#object file definition
OBJS = $(OBJ_PATH)CallBackFunc.o $(OBJ_PATH)CoDirect.o $(OBJ_PATH)CoIED.o $(OBJ_PATH)Checkup.o \
	$(OBJ_PATH)ga_comtrade.o $(OBJ_PATH)GADirect.o $(OBJ_PATH)I_IED_Run.o $(OBJ_PATH)IED.o \
	$(OBJ_PATH)ThreadManger.o $(OBJ_PATH)FailReason.o $(OBJ_PATH)SYCommunDllPack.o \
	$(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o $(OBJ_PATH)CUnTime.o \
	$(OBJ_PATH)tinystr.o $(OBJ_PATH)tinyxml.o $(OBJ_PATH)tinyxmlparser.o $(OBJ_PATH)tinyxmlerror.o

nx_ga_tcpclient_61850.so : $(OBJS) mkoutdir
	g++ -o  $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic
	@echo ===== Build Date: `date` =====  | tee -a  $(OUT_PATH)$(OBJ_NAME)
	@echo =====PRJ_COMMON===== ${PRJ_COMMON} == $(GA_TYPE) | tee -a  $(OUT_PATH)$(OBJ_NAME)
	find ${COSMOS_PATH} -type f | grep -v .svn |xargs md5sum >>  $(OUT_PATH)$(OBJ_NAME)

$(OBJ_PATH)CallBackFunc.o : ./CallBackFunc.cpp mkobjdir
	g++ -o $(OBJ_PATH)CallBackFunc.o \
		-c ./CallBackFunc.cpp $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)CoDirect.o : ./CoDirect.cpp
	g++ -o $(OBJ_PATH)CoDirect.o \
		-c ./CoDirect.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)CoIED.o : ./CoIED.cpp
	g++ -o $(OBJ_PATH)CoIED.o \
		-c ./CoIED.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)tinystr.o : ./tinystr.cpp
	g++ -o $(OBJ_PATH)tinystr.o \
		-c ./tinystr.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)tinyxml.o : ./tinyxml.cpp
	g++ -o $(OBJ_PATH)tinyxml.o \
		-c ./tinyxml.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)tinyxmlerror.o : ./tinyxmlerror.cpp
	g++ -o $(OBJ_PATH)tinyxmlerror.o \
		-c ./tinyxmlerror.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)tinyxmlparser.o : ./tinyxmlparser.cpp
	g++ -o $(OBJ_PATH)tinyxmlparser.o \
		-c ./tinyxmlparser.cpp $(CFLAGS) $(VPATH)
		
$(OBJ_PATH)Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -o $(OBJ_PATH)Checkup.o \
		-c ../../nx_ga_common/Checkup.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -o $(OBJ_PATH)ga_comtrade.o \
		-c ../../nx_ga_common/ga_comtrade.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -o $(OBJ_PATH)GADirect.o \
		-c ../../nx_ga_common/GADirect.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)IED.o : ../../nx_ga_common/IED.cpp
	g++ -o $(OBJ_PATH)IED.o \
		-c ../../nx_ga_common/IED.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -o $(OBJ_PATH)I_IED_Run.o \
		-c ../../nx_ga_common/I_IED_Run.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)ThreadManger.o : ../../nx_ga_common/ThreadManger.cpp
	g++ -o $(OBJ_PATH)ThreadManger.o \
		-c ../../nx_ga_common/ThreadManger.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -o $(OBJ_PATH)FailReason.o \
		-c ../../nx_ga_common/FailReason.cpp $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -o $(OBJ_PATH)SYCommunDllPack.o \
		-c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -o $(OBJ_PATH)CsgLogRecord.o \
		-c ../../../../../nx_common/CsgLogRecord.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -o $(OBJ_PATH)CsgLogRecordMngr.o \
		-c ../../../../../nx_common/CsgLogRecordMngr.cpp $(CFLAGS) $(VPATH)
		
$(OBJ_PATH)CUnTime.o : ../../../../../nx_common/UnTime.cpp
	g++ -o $(OBJ_PATH)CUnTime.o \
		-c ../../../../../nx_common/UnTime.cpp $(CFLAGS) $(VPATH)

mkobjdir : 
	if [ -e ../../../../../platform_include/external/cosmos/lib/linux/libcosmos_l.so ]; then rm -f ../../../../../platform_include/external/cosmos/lib/linux/libcosmos_l.so; fi
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
mkoutdir : 
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi

.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
