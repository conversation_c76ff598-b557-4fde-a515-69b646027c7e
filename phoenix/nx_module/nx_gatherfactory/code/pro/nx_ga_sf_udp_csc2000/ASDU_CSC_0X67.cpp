#include "ASDU_CSC_0X67.h"
#include "ASDU_common.h"


CAsdu67::CAsdu67( string& str_packet )
{
	unpacket(str_packet);
}

CAsdu67::CAsdu67()
{
	
}

CAsdu67::~CAsdu67()
{
	
}

void CAsdu67::unpacket( const string& str_packet )
{
	BYTE* pbuff =(BYTE*) str_packet.data();
	
	int off = m_osc_base_info.unpacket(pbuff);
	pbuff += off;

	pbuff += 1;

	off = m_osc_info.unpacket(pbuff);
	pbuff += off;

	m_window_bytes = cal_word_for_low_to_high(pbuff);
	pbuff += 2;

	m_max_frame_length = *pbuff;
	pbuff += 1;

	m_filesize = cal_dword_for_low_to_high(pbuff);
}

void CAsdu67::init()
{
	memset(&m_osc_base_info, 0x00, sizeof( m_osc_base_info ));
	m_osc_info.m_str_filename = "";
	m_osc_info.m_str_filetype = "";
	m_max_frame_length = -1;
	m_filesize = -1;
	
}

New_Csc_base_info CAsdu67::get_osc_base_info() const
{
	return m_osc_base_info;
}

New_Osc_info CAsdu67::get_osc_info() const
{
	return m_osc_info;
}

int CAsdu67::get_window_bytes() const
{
	return m_window_bytes;
}

int CAsdu67::get_filesize() const
{
	return m_filesize;	
}

int CAsdu67::get_max_frame_length() const
{
	return m_max_frame_length;
	
}
