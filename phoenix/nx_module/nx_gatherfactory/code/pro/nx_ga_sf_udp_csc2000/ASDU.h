#if !defined( AFX_ASDU23_FOR_103_H )
#define AFX_ASDU23_FOR_103_H

#include "assistdef.h"
#include "ASDU_CSC_common.h"

const int BUFF_SIZE = 1024;
typedef struct
{
    int		m_len;			
    BYTE		m_buff[ BUFF_SIZE ];
} read_write_buff;


template< class T > 
inline void init_read_or_write_buf(  T* pT )
{
  memset( pT->Buff,0x00,sizeof( pT->Buff ) );
  pT->Len = 0;
}

template<typename T>
T  creat_T(read_write_buff *pRead, int data_col_num)
{
	string packet((char*)&pRead->m_buff[0], (char*)&pRead->m_buff[pRead->m_len]);
	T asdu(data_col_num, packet);

	return asdu;
}


template<typename T>
T  creat_T(string packet, int data_col_num)
{
	T asdu(data_col_num, packet);

	return asdu;
}

template<typename T>
T creat_head(read_write_buff *pRead)
{
	string packet((char*)&pRead->m_buff[0], (char*)&pRead->m_buff[pRead->m_len]);
	T head;
	if (pRead->m_len == 0)
	{
		return head;
	}
	head.unpacket_pre_data_col(packet);

	return head;
}

template<typename T>
T creat_head(string str_packet)
{
	string packet = str_packet;
	if (is_ascii(str_packet))
	{	
		packet=convert_byte_format( str_packet );
	}
	T head;

	if (str_packet.empty())
	{
		return head;
	}

	head.unpacket_pre_data_col(packet);

	return head;
}


template< typename T>
bool Is_T(read_write_buff *pRead, int data_col_num, int addr, int cpu_no )
{
	
	T asdu = creat_T<T> (pRead, data_col_num);

	return asdu.get_addr() == addr && asdu.get_cpu_no() == cpu_no;
}

template< typename T>
bool Is_T(read_write_buff *pRead, int data_col_num, int addr, int cpu_no, int trouble_no )
{
	T asdu = creat_T<T> (pRead, data_col_num);

	return asdu.get_addr() == addr && asdu.get_cpu_no() == cpu_no && asdu.get_itrouble_no() == trouble_no;
}


string get_str_packet(BUS_FRAME_OP* pread_write_buff);




#endif //#AFX_ASDU23_FOR_103_H
