#include "ASDU_CSC_0X38.h"
#include "assistdef.h"


CAsdu38::CAsdu38( string str_packet , bool is_new_version)
{
	m_is_new_version = is_new_version;
	unpacket(str_packet);
}

CAsdu38::CAsdu38()
{
	
}

CAsdu38::~CAsdu38()
{
	
}

void CAsdu38::unpacket( const string& str_packet )
{
	BYTE* pbuff =(BYTE*) str_packet.data();
		
	int  off = m_base_info.unpacket(pbuff);
	pbuff += off;
		  
	if(m_is_new_version)
	{
		m_bt_control_code = *pbuff;
		pbuff += 1;
		
		m_bt_cpuaddr = *pbuff;
		pbuff += 1;
		
		m_absolute_time = get_time_for_severnbyte(&pbuff[ 0 ]);
		pbuff += 7;
	}
	else
	{
		m_absolute_time = get_time_for_fourByte(&pbuff[ 0 ]);
		pbuff += 4;
		m_bt_control_code = 0;
		m_bt_cpuaddr = 0;
	}
	
	m_baohu_type = *pbuff;
	pbuff += 1;
	
	m_event_code = *pbuff;
	pbuff += 1;

	if(!m_is_new_version)
	{
		return ;
	}
	
	int param_num =  *pbuff;
	pbuff += 1;
	
	for (int  i = 0 ; i < param_num ; i++)
    {
		param_info info;
		memset(&info,  0x00, sizeof( info ) );
		
		int offset = 0;
		info.unpacket(&pbuff[ 0 ], offset);
		pbuff += offset;
		
		m_vec_param_info.push_back(info);
    }
}

CAsdu38& CAsdu38::operator=( const CAsdu38& rhs )
{
	this->m_absolute_time = rhs.m_absolute_time;
	this->m_baohu_type = rhs.m_baohu_type;
	this->m_base_info = rhs.m_base_info;
	this->m_bt_control_code = rhs.m_bt_control_code;
	this->m_bt_cpuaddr = rhs.m_bt_cpuaddr;
	this->m_event_code =rhs.m_event_code;
	this->m_vec_param_info =rhs.m_vec_param_info;
	
	return *this;
}

bool operator==( const CAsdu38& lhs, const CAsdu38& rhs )
{
	if (lhs.m_base_info == rhs.m_base_info &&
		lhs.m_bt_control_code == rhs.m_bt_control_code &&
		lhs.m_bt_cpuaddr == rhs.m_bt_cpuaddr &&
		lhs.m_event_code == rhs.m_event_code &&
		equal_time(lhs.m_absolute_time, rhs.m_absolute_time) &&
		lhs.m_vec_param_info.size() == rhs.m_vec_param_info.size()&&
		equal(lhs.m_vec_param_info.begin(), lhs.m_vec_param_info.end(), rhs.m_vec_param_info.begin()))
	{
		return true;
	}
	
	return false;
	
}


MY_TIME_INFO CAsdu38::get_absolute_time() const
{
	return m_absolute_time;
}


BYTE CAsdu38::get_baohu_type() const
{
	return m_baohu_type;
}

BYTE CAsdu38::get_event_code() const
{
	return m_event_code;
}

