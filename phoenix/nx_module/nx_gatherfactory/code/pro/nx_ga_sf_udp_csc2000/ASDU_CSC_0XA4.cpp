#include "ASDU_CSC_0XA4.h"
#include "ASDU_common.h"

CAsduA4::CAsduA4( string& str_packet )
{
	unpacket(str_packet);
}

CAsduA4::CAsduA4()
{
	
}

CAsduA4::~CAsduA4()
{
	
}

void CAsduA4::unpacket( const string& str_packet )
{

	BYTE* pbuff =(BYTE*) str_packet.data();

	int off = m_osc_base_info.unpacket(pbuff);
	pbuff += off;

	m_relative_start_time = cal_word_for_low_to_high(pbuff);
	pbuff += 2;

	int data_type = 	m_osc_base_info.m_datatype;
	assert(data_type == 0xA4 || data_type == 0XB4 || data_type == 0xA6 ||  data_type == 0xB6);
}


int CAsduA4::get_relative_start_time() const
{
	return m_relative_start_time;
}



int CAsduA4::get_asdu_no() const
{
	
  return m_osc_base_info.m_asdu_no;
}

int CAsduA4::get_main_station_addr() const
{
  return m_osc_base_info.m_main_station_addr;
}

int CAsduA4::get_addr() const
{
  return m_osc_base_info.m_addr;
}

int CAsduA4::get_packet_index() const
{
  return m_osc_base_info.m_packet_index;	
}

int CAsduA4::get_datatype()
{
	return m_osc_base_info.m_datatype;
}
