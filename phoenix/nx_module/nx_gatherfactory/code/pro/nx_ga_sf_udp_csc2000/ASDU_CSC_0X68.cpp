#include "ASDU_CSC_0X68.h"
#include "ASDU_common.h"


CAsdu68::CAsdu68( New_Csc_base_info osc_base_info, int packet_index )
{
	m_osc_base_info = osc_base_info;
	m_packet_index = packet_index;
	
}

CAsdu68::~CAsdu68()
{
	
}

void CAsdu68::packet( BYTE * pbuff )
{
	int off = 0;
	off = packet_common_head(pbuff);
	pbuff += off;
	
	off = m_osc_base_info.packet(pbuff);
	pbuff += off;

	pbuff += 1;

	off = packet_word_for_low_to_high(pbuff, m_packet_index);
	pbuff += off;
	
}
