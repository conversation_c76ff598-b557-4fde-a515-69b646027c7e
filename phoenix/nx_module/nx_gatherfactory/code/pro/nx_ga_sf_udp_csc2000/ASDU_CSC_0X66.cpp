#include "ASDU_CSC_0X66.h"
#include "ASDU_common.h"


CAsdu66::CAsdu66( New_Csc_base_info osc_base_info, New_Osc_info osc_info, int window_bytes , int max_frame_length )
{
	m_osc_base_info = osc_base_info;
	m_osc_info = osc_info;
	m_window_bytes = window_bytes;
	m_max_frame_length = max_frame_length;
}

CAsdu66::~CAsdu66()
{
	
}

void CAsdu66::packet( BYTE* pbuff )
{
	int off = 0;
	off = packet_common_head(pbuff);
	pbuff += off;
	
	off = m_osc_base_info.packet(pbuff);
	pbuff += off;
	
	off = m_osc_info.packet(pbuff);
	pbuff += off;
	
	off = packet_word_for_low_to_high(pbuff, m_window_bytes);
	pbuff += off;
	
	pbuff[ 0 ] = m_max_frame_length;
}
