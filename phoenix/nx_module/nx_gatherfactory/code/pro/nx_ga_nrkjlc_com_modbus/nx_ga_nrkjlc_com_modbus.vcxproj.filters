﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="common">
      <UniqueIdentifier>{8911fd42-ac9f-4c8f-8bce-564f1db4d526}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CoIED.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\GADirect.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\IED.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\ga_functions.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="CoDirect.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="CalByte.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecord.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FailReason.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\FindSortFuns.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\nx_ga_common\ga_dfrlist.h">
      <Filter>common</Filter>
    </ClInclude>
    <ClInclude Include="assistdef.h">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CoIED.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\GADirect.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\I_IED_Run.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\IED.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\Checkup.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\FailReason.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_functions.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\platform_include\plm_commun\SYCommunDllPack.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ThreadManger.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_comtrade.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="CoDirect.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="CalByte.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\..\nx_common\CsgLogRecord.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\nx_ga_common\ga_dfrlist.cpp">
      <Filter>common</Filter>
    </ClCompile>
    <ClCompile Include="assistdef.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
</Project>