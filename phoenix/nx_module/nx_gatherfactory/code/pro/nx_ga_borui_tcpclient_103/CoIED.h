﻿/****************************************************************
*  CoIED.h       author: yk      date: 18/04/2018
*----------------------------------------------------------------
*  note: 实现武仪录波器采集规约类
*  
*****************************************************************/

#ifndef _COIED_H_
#define _COIED_H_

#include <bitset>
#include "IED.h"
#include "ga_dfrlist.h"
#include "FindSortFuns.h"
#include "ga_comtrade.h"

#define IS_COMPRESSION      0   //是否压缩
#define OP_SUCCESS        0X46  //操作成功码
#define OP_FAIL        0X45  //操作失败码
#define MAX_PATH_LENGTH  256

#ifdef __PLATFORM_MS_WIN__

const char    FOLD_OPTION = '\\';
const string  FILE_FOLD_OPTION = "\\";

#else

#define HIBYTE( w ) ( ( BYTE )( ( ( WORD )( w ) >> 8 ) & 0xFF ) )
#define LOBYTE( w ) ( ( BYTE )( w ) )
#define LOWORD( l ) ( ( WORD )( l ) )
#define HIWORD( l ) ( ( WORD )( ( ( ulong )( l ) >> 16 ) & 0xFFFF ) )
const char    FOLD_OPTION = '/';
const string  FILE_FOLD_OPTION = "/";
#define DWORD ulong
#define MAX_PATH 260

#endif


const DWORD BUFF_DFR_SIZE = 2048;
const int MAX_PASSWORD_LEN = 15;  //规约允许的最长密码长度
const int MAX_USERNAME_LEN = 48;  //规约允许的最长用户名长度

// (注意 该报文头与规约文档中说的报文头意思不一样,这里指的是 从开始字节起到信息开始处之间的数据长度, 这么做主要是程序处理方便)
const int PACKET_HEAD_LEN = 8;  //读报文时，要读报文头的长度 

const int PACKET_TAIL_LEN = 2 ;  //读报文时，要读报文尾的长度（ 校验和位 + 0X16)
const int ASDU80_FILECONT_LEN = 100 ;  //ASDU80中文件内容的长度
const int FUN_TIMEOUT = 10;
const int ASDU_TYPE_POS = 4; //ASDU 类型字节的 位置  即控制域
const int ASDU_LEN_POS = 6; //ASDU 表示长度字节的位置
const BYTE ASDU_END_BYTE = 0X16; //ASDU结束字节

const int PACKET_LENGTH = 1024;

#define MAX_STR_LONG 100
#define LAST_LIST_FILE		"LastListFile.lst"

const string LIST_FILENAME="history.dat";
const string filename_for_Switch="switch.dat";
const string filename_for_analog="analog.dat";
const string filename_for_highfreq="highfreq.dat";

const string cfg_ext_name = ".cfg";
const string dat_ext_name = ".dat";
const string hdr_ext_name = ".hdr";

struct Lb_fileInf
{
	char filename[MAX_PATH];//录波文件名称
	char fault_type[MAX_STR_LONG];//故障类型
	char fault_channel[MAX_STR_LONG];//相关通道
	char fault_start_alog[MAX_STR_LONG];//启动算法
	float fault_distance;//故障测距，浮点数

};


struct file_info
{
	string filedata; 
	long file_length;
	string filename;
};



#pragma once
class CCoIED : public CIED
{
public:
	CCoIED(void);

	/**
	* @brief     		构造函数
	* @param[out]		pFactory，采集工厂属性指针
	* @param[out]		p_st_Ied, IED属性指针
	* @param[out]		pCoIEDLog，日志属性指针
	* @return			无
	*/
	CCoIED(FACTORY_ATBUTE* pFactory, IED_ATBUTE* p_st_Ied, CLogRecord* pCoIEDLog);

	~CCoIED(void);

	/**
	* @brief			召唤指定LD的文件列表(当pStart_time = NULL &&  pEnd_time = NULL, 表示召唤当前最新文件列表 )
	* @param[in]		nld	LD编号
	* @param[in]		pStart_time	起始时间   
	* @param[in]		pEnd_time	结束时间
	* @return			TRUE：成功， FALSE：失败		           
	*/
	BOOL CallFileList(int nld, MY_TIME_INFO* pStart_time, MY_TIME_INFO* pEnd_time);

	/**
	* @brief			召唤指定的波形文件
	* @param[in]		filename 需要召唤的文件名称
	* @return			TRUE：成功， FALSE：失败
	* @note                        
	*/
	BOOL CallFile(string& filename);

	/**
	* @brief     	    召唤所有新文件
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CallAllFile();

	/*召唤通讯状态*/
	BOOL CallState(int nld);
		/**
	* @brief     	 重新登录
	* @param[in]     无
	* @param[out]    无
	* @return        BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL Relogin();
private:
	/** @brief          当前cpu*/
	u_int8 m_nCurCpu;

	/** @brief			召唤录波方式   */
	u_int8 m_u_CallFileType;

	/** @brief			本地录波存放目录（绝对路径）*/
	string m_strWavePath;

	/** @brief          录波器工作目录*/
	string m_strWorkPath;

	/** @brief          装置有新录波标志*/
	bool m_bNewWaveFlag;

	/** @brief			录波文件列表处理类对象*/
	CDFRList* m_pCurDfrList;

	/** @brief			当前文件列表*/
	vector<DFR_FILE_INF> m_vCurFileList;

	/** @brief			待召唤文件列表*/
	vector<DFR_FILE_INF> m_vCallFileList;

	/** @brief			新文件列表*/
	vector<DFR_FILE_INF> m_vNewFileList;

	/** @brief          存储在本地的定值全路径*/
	string m_strLocalSGFile;

private:
	/**
	* @brief     		初始化各项配置
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitIED();

	/**
	* @brief     	初始化装置通信状态
	* @param[in]    无
	* @param[out]   无
	* @return       void
	*/
	void InitIedState();

	/**
	* @brief     	打开通讯端口
	* @param[in]    无
	* @param[out]   无
	* @return       BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL OpenCommun();

	/**
	* @brief     		初始化网络通讯
	* @param[in]		无
	* @param[out]		无
	* @return			BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL InitNetwork();

	/**
	* @brief     	    创建通讯对象, 负责创建通讯需要的通讯对象(总线或者点对点)
	* @param[out]       pDllPack 通讯库包装对象
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL CreateCommTranObj(CSYCommunDllPack* pDllPack);

	/**
	* @brief		初始化所有LD的的通讯状态，并得到装置最终状态
	* @param		无
	* @return		无         
	*/
	void InitAllLDState();
	
	/**
	* @brief     		 发送报文
	* @param[in]     	 无
	* @param[out]    	 无
	* @return          	 int,0 成功 1失败
	*/
	int SendData();

	/**
	* @brief     	    从socket接收数据
	* @param[in]        无
	* @param[out]       无
	* @return           int，0成功 －1失败， －2 超时
	*/
	int RecvData();	


	/**
	* @brief     	    接收报文
	* @param[in]        无
	* @param[out]       无
	* @return           BOOL,TRUE:成功    FALSE:失败
	*/
	BOOL ReadData();

	/**
	* @brief     	以下变量与列目录相关
	*/


	char recv_message_catalogue_data_filename[13] ;  //定义一个字符型数组用来存放解析出来的文件名

	string st_recv_message_catalogue_data_filename_suffix;  //定义一个string用来执行去后缀操作
    string st_recv_message_catalogue_data_filename;         //定义一个string用来存放去后缀之后的文件名

	vector<Lb_fileInf> m_vecFileList;                     //定义一个vector用来存放文件名和其对应的时间

	void Pack_CallFileList(WORD dwGroupNum, WORD dwGroupNo); //打包召唤文件列表报文
	void DataProcess_catalogue() ;               //解析召唤文件列表报文
	MY_TIME_INFO GetTimeFromFileName(char* pFileName);


	/**
	* @brief     	以下变量或函数与读文件相关
	*/
	string filename_temporary;                                   //用来存放上面下发的filename，这个变量的目的是不改变filename。
	string filename_dat;                                         //将上面下发的无后缀名的filename转化为filename.dat后存放在这个变量中
	string filename_hdr;                                         //hdr    
	string filename_cfg;                                         //cfg
	string filename_lne;


	BYTE recv_message_file_data_code;                             //用来存放读文件接收报文中的CODE码
	WORD recv_message_file_data_groupnum;                       //用来存放读文件接收报文中的分组数
	WORD recv_message_file_data_groupno;                        //用来存放读文件接收报文中的分组序号
	BYTE recv_message_file_data_length;                         //用来存放读文件接收报文中的报文长度（减去6就是DATA部分的长度）

	void Pack_ReadFile_First(string &filename_suffix);           //包装第一帧读文件报文
	void Pack_ReadFile_Continue(WORD dwGroupNum,WORD dwGroupNo);  //包装后续读文件报文
	BOOL CallFile_suffix(string& path_filename_suffix,string& filename_suffix); //召唤不同后缀名的文件
	void DataProcess_ReadFile(char* pData, int& nSize);           //解析读文件报文

    /**以下变量或函数与召唤通讯状态相关**/
	
	bool request_state();

	bool request_login();
	void Set_Login_Packet(string stm_username,string m_password);
	string m_username;
	string m_password;
	int m_iSourceAddr;

	bool checkup();
	bool is_login();
	void set_common_packet( int control,int addr,bool have_compress,char* message,int message_length );
	/**以下变量或函数与召唤文件列表相关**/
	bool request_one_file(bool bauto = false);
	file_info m_asdu80;
	file_info m_current_file_info;  //当前召唤文件的文件信息（如文件内容、文件名等）见结构file_info
	string m_str_osc_fullpath;  //完全路径
	string get_diskname( string str_fullpath, string filename);
	void set_filedata_packet(long start_pos,int data_length);  //发送ASDU47
	void distill_asdu80();   //解析ASDU80
	void distill_asdu68(bool bLastFlag);   //解析ASDU68
	void packet_word_for_low_to_high(BYTE* pBuff, WORD value);
	void packet_dword_for_low_to_high(BYTE* pBuff, DWORD value);
	bool is_file_end();
	void set_op_sucess_packet();//发送确认帧
	bool distill_lb_list();
	bool read_file_data( string filename,vector<string>& vec_filedata );
	bool distll_one_lb_list( string line_info, Lb_fileInf& fileinf);
	vector<string> extract_all_string_value(string str_value);
	string split(string const & a, string & piece, char delim);
	string strip(string const & a, char const c);
	string get_ext_filename( string filename);

	/**以下变量或函数与召唤指定文件相关**/
	bool find_lblist_item(const vector<Lb_fileInf>& vec_full_list, char* filename, Lb_fileInf& finded_lblist_item);
	Lb_fileInf m_current_lbfileInf; //目前正在召唤的录波信息
	bool requestlbfile();
	void set_filename_packet(string filename);
	bool request_all_file();
	bool is_file_first_frame();
	bool is_asdu68();
//	vector<Lb_fileInf> m_vec_faultfile_info;


};

#endif