#define the path of search
VPATH = -I./ \
				-I../../nx_ga_common \
				-I../../../../../nx_common \
				-I../../../../../platform_include/plm_common \
				-I../../../../../platform_include/plm_commun 
LABLE = 1.0.1

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OUT_PATH = ../../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../../nx_lib/release/
	OBJ_PATH = ./release/
	OBJ_NAME = nx_ga_abb_com_101.so-$(LABLE)
else
	RUN_FLAGS = -g
	OUT_PATH = ../../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../../nx_lib/debug/
	OBJ_PATH = ./debug/
	OBJ_NAME = nx_ga_abb_com_101.so
endif

LIBS =  -L./ -L$(OBJ_PATH) -lpthread -L$(LIB_PATH) -lsylib -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__

#object file definition
OBJS = $(OBJ_PATH)CoIED.o $(OBJ_PATH)CoDirect.o $(OBJ_PATH)IED.o $(OBJ_PATH)Checkup.o \
			$(OBJ_PATH)GADirect.o $(OBJ_PATH)I_IED_Run.o $(OBJ_PATH)ThreadManger.o $(OBJ_PATH)ga_dfrlist.o \
			$(OBJ_PATH)ga_comtrade.o $(OBJ_PATH)FailReason.o $(OBJ_PATH)ga_functions.o $(OBJ_PATH)SYCommunDllPack.o \
			$(OBJ_PATH)CsgLogRecord.o $(OBJ_PATH)CsgLogRecordMngr.o

nx_ga_abb_com_101.so : $(OBJS) mksylibdir
	g++ -o $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

$(OBJ_PATH)CoIED.o : ./CoIED.cpp mkobjdir
	g++ -o $(OBJ_PATH)CoIED.o -c ./CoIED.cpp $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)CoDirect.o : ./CoDirect.cpp
	g++ -o $(OBJ_PATH)CoDirect.o -c ./CoDirect.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)IED.o : ../../nx_ga_common/IED.cpp
	g++ -o $(OBJ_PATH)IED.o -c ../../nx_ga_common/IED.cpp $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -o $(OBJ_PATH)Checkup.o -c ../../nx_ga_common/Checkup.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -o $(OBJ_PATH)GADirect.o -c ../../nx_ga_common/GADirect.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -o $(OBJ_PATH)I_IED_Run.o -c ../../nx_ga_common/I_IED_Run.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)ThreadManger.o : ../../nx_ga_common/ThreadManger.cpp
	g++ -o $(OBJ_PATH)ThreadManger.o -c ../../nx_ga_common/ThreadManger.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)ga_dfrlist.o : ../../nx_ga_common/ga_dfrlist.cpp
	g++ -o $(OBJ_PATH)ga_dfrlist.o -c ../../nx_ga_common/ga_dfrlist.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -o $(OBJ_PATH)ga_comtrade.o -c ../../nx_ga_common/ga_comtrade.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -o $(OBJ_PATH)FailReason.o -c ../../nx_ga_common/FailReason.cpp $(CFLAGS) $(VPATH)
	
$(OBJ_PATH)ga_functions.o : ../../nx_ga_common/ga_functions.cpp
	g++ -o $(OBJ_PATH)ga_functions.o -c ../../nx_ga_common/ga_functions.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -o $(OBJ_PATH)SYCommunDllPack.o -c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -o $(OBJ_PATH)CsgLogRecord.o -c ../../../../../nx_common/CsgLogRecord.cpp $(CFLAGS) $(VPATH)

$(OBJ_PATH)CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -o $(OBJ_PATH)CsgLogRecordMngr.o -c ../../../../../nx_common/CsgLogRecordMngr.cpp $(CFLAGS) $(VPATH)

mksylibdir : 
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi

mkobjdir : 
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
