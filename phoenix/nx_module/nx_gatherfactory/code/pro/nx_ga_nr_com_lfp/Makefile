#define the path of search
VPATH = -I./ \
				-I../../nx_ga_common \
				-I../../../../../nx_common \
				-I../../../../../platform_include/plm_common \
				-I../../../../../platform_include/plm_commun 
LABLE = 1.0.0

#cflags definition
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	OBJ_PATH = ../../../../../nx_bin/release/ga/
	LIB_PATH = ../../../../../nx_lib/release/
	OBJ_NAME = nx_ga_nr_com_lfp.so-$(LABLE)
else
	RUN_FLAGS = -g
	OBJ_PATH = ../../../../../nx_bin/debug/ga/
	LIB_PATH = ../../../../../nx_lib/debug/
	OBJ_NAME = nx_ga_nr_com_lfp.so
endif

LIBS =  -L./ -L$(OBJ_PATH) -lpthread -L$(LIB_PATH) -lsylib -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__

#object file definition
OBJS = CoIED.o CoDirect.o LFPBase.o LFP901A.o LFP902A.o LFP902C.o LFP923A.o LFP941A.o \
			IED.o Checkup.o GADirect.o I_IED_Run.o ThreadManger.o ga_comtrade.o FailReason.o \
			SYCommunDllPack.o CsgLogRecord.o CsgLogRecordMngr.o

nx_ga_nr_com_lfp.so : $(OBJS) mksylibdir
	g++ -o  $(OBJ_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -shared -fpic

CoIED.o : ./CoIED.cpp
	g++ -c ./CoIED.cpp $(CFLAGS) $(VPATH)
	
CoDirect.o : ./CoDirect.cpp
	g++ -c ./CoDirect.cpp $(CFLAGS) $(VPATH)

LFPBase.o : ./LFPBase.cpp
	g++ -c ./LFPBase.cpp $(CFLAGS) $(VPATH)

LFP901A.o : ./LFP901A.cpp
	g++ -c ./LFP901A.cpp $(CFLAGS) $(VPATH)

LFP902A.o : ./LFP902A.cpp
	g++ -c ./LFP902A.cpp $(CFLAGS) $(VPATH)

LFP902C.o : ./LFP902C.cpp
	g++ -c ./LFP902C.cpp $(CFLAGS) $(VPATH)

LFP923A.o : ./LFP923A.cpp
	g++ -c ./LFP923A.cpp $(CFLAGS) $(VPATH)

LFP941A.o : ./LFP941A.cpp
	g++ -c ./LFP941A.cpp $(CFLAGS) $(VPATH)

IED.o : ../../nx_ga_common/IED.cpp
	g++ -c ../../nx_ga_common/IED.cpp $(CFLAGS) $(VPATH)
	
Checkup.o : ../../nx_ga_common/Checkup.cpp
	g++ -c ../../nx_ga_common/Checkup.cpp $(CFLAGS) $(VPATH)

GADirect.o : ../../nx_ga_common/GADirect.cpp
	g++ -c ../../nx_ga_common/GADirect.cpp $(CFLAGS) $(VPATH)

I_IED_Run.o : ../../nx_ga_common/I_IED_Run.cpp
	g++ -c ../../nx_ga_common/I_IED_Run.cpp $(CFLAGS) $(VPATH)

ThreadManger.o : ../../nx_ga_common/ThreadManger.cpp
	g++ -c ../../nx_ga_common/ThreadManger.cpp $(CFLAGS) $(VPATH)

ga_comtrade.o : ../../nx_ga_common/ga_comtrade.cpp
	g++ -c ../../nx_ga_common/ga_comtrade.cpp $(CFLAGS) $(VPATH)
	
FailReason.o : ../../nx_ga_common/FailReason.cpp
	g++ -c ../../nx_ga_common/FailReason.cpp $(CFLAGS) $(VPATH)
	
CsgLogRecord.o : ../../../../../nx_common/CsgLogRecord.cpp
	g++ -c ../../../../../nx_common/CsgLogRecord.cpp $(CFLAGS) $(VPATH)

CsgLogRecordMngr.o : ../../../../../nx_common/CsgLogRecordMngr.cpp
	g++ -c ../../../../../nx_common/CsgLogRecordMngr.cpp $(CFLAGS) $(VPATH)

SYCommunDllPack.o : ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp
	g++ -c ../../../../../platform_include/plm_commun/SYCommunDllPack.cpp $(CFLAGS) $(VPATH)

mksylibdir : 
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists";   else mkdir -p $(OBJ_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OBJ_PATH)$(OBJ_NAME) $(OBJS)
