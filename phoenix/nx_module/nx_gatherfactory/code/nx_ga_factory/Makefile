#define the path of search
VPATH = -I./ -I../nx_ga_common -I../../../../nx_common -I../../../../platform_include/plm_common -I../../../../platform_include/plm_commun
LABLE = 1.0.10

#cflags definition
_D=Y
ifeq ($(_D),N)
	RUN_FLAGS = -O2
	MYLIB_PATH  = -L../../../../nx_lib/release/
	OUT_PATH = ../../../../nx_bin/release/ga/
	OBJ_PATH = ./release/
	OBJ_NAME = nx_ga_factory-$(LABLE)
else
	RUN_FLAGS = -g
	MYLIB_PATH  = -L../../../../nx_lib/debug/
	OUT_PATH = ../../../../nx_bin/debug/ga/
	OBJ_PATH = ./debug/
	OBJ_NAME = nx_ga_factory
endif

GA_TYPE=Z2000_GA
ifeq ($(Z2800_GA),Y)
	GA_TYPE=Z2800_GA
endif

LIBS =  $(MYLIB_PATH) -L$(OUT_PATH) -lpthread -lsylib -lplm_db -ldl -rdynamic -lrt

CFLAGS = $(RUN_FLAGS) -D__PLATFORM_OPEN_LINUX__ -D$(GA_TYPE)

#object file definition
OBJS = $(OBJ_PATH)GAOper.o $(OBJ_PATH)gather.o $(OBJ_PATH)ThreadManger.o

$(OBJ_NAME) : $(OBJS) mksylibdir
	g++ -o  $(OUT_PATH)$(OBJ_NAME) $(OBJS) $(LIBS) $(CFLAGS) -fpic

$(OBJ_PATH)GAOper.o : GAOper.cpp mkobjdir
	g++ -o $(OBJ_PATH)GAOper.o $(CFLAGS) $(VPATH) \
	    -c GAOper.cpp

$(OBJ_PATH)gather.o : gather.cpp
	g++ -o $(OBJ_PATH)gather.o $(CFLAGS) $(VPATH) \
	    -c  gather.cpp 

$(OBJ_PATH)ThreadManger.o :../nx_ga_common/ThreadManger.cpp

	g++ -o $(OBJ_PATH)ThreadManger.o $(CFLAGS) $(VPATH) \
	-c ../nx_ga_common/ThreadManger.cpp

mkobjdir:
	if [ -d $(OBJ_PATH) ]; then echo "$(OBJ_PATH) exists;";   else mkdir -p $(OBJ_PATH); fi

mksylibdir:
	if [ -d $(OUT_PATH) ]; then echo "$(OUT_PATH) exists";   else mkdir -p $(OUT_PATH); fi
	
.PHONY : all
all: main

.PHONY : install
install:
	@echo nothing done

.PHONY : print
print:
	@echo nothing done

.PHONY : tar
tar:
	@echo nothing done

.PHONY : clean
clean:
	rm -f $(OUT_PATH)$(OBJ_NAME) $(OBJS)
