/**
 * @file protocol_client.hpp
 * @brief 简化的IEC 60870-5-103协议客户端
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HPP

#include <functional>
#include <memory>
#include <string>
#include <atomic>
#include <mutex>
#include "zexuan/base/base_types.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/event/net/tcp_client.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/event_loop_thread.hpp"

// 前向声明
namespace zexuan {
namespace protocol {
namespace client {
    class IMessageTypeHandler;
    class MessageTypeHandlerFactory;
    struct MessageRequest;
}
}
}

namespace zexuan {
namespace protocol {
namespace client {

/**
 * @brief 简化的协议客户端
 *
 * 保留handlers路由机制的简化实现：
 * 1. TCP连接管理（集成TcpConnectionManager功能）
 * 2. 消息发送（通过handlers factory路由）
 * 3. 消息接收与解析（集成MessageFrameParser功能）
 * 4. 回调通知
 * 5. 自动handler路由
 */
class ProtocolClient {
public:
    // 回调函数类型定义
    using ConnectionCallback = std::function<void(bool connected)>;
    using MessageCallback = std::function<void(const base::Message& message)>;

    /**
     * @brief 构造函数
     * @param server_host 服务器地址
     * @param server_port 服务器端口
     */
    ProtocolClient(const std::string& server_host, uint16_t server_port);

    /**
     * @brief 析构函数
     */
    ~ProtocolClient();

    // 禁止拷贝和移动
    ProtocolClient(const ProtocolClient&) = delete;
    ProtocolClient& operator=(const ProtocolClient&) = delete;
    ProtocolClient(ProtocolClient&&) = delete;
    ProtocolClient& operator=(ProtocolClient&&) = delete;

    /**
     * @brief 启动客户端
     * @return 启动结果
     */
    base::Result<void> Start();

    /**
     * @brief 停止客户端
     * @return 停止结果
     */
    base::Result<void> Stop();

    /**
     * @brief 连接到服务器
     * @return 连接结果
     */
    base::Result<void> Connect();

    /**
     * @brief 断开连接
     */
    void Disconnect();

    /**
     * @brief 检查是否已连接
     * @return true: 已连接, false: 未连接
     */
    bool IsConnected() const;

    /**
     * @brief 发送Type1消息（单帧测试）
     * @param target 目标地址
     * @param test_data 测试数据（可选）
     * @return 发送结果
     */
    base::Result<void> SendType1Message(uint8_t target, const std::string& test_data = "");

    /**
     * @brief 发送Type2消息（多帧测试）
     * @param target 目标地址
     * @param test_data 测试数据（可选）
     * @return 发送结果
     */
    base::Result<void> SendType2Message(uint8_t target, const std::string& test_data = "");

    /**
     * @brief 发送Type3消息（时间戳事件）
     * @param target 目标地址
     * @return 发送结果
     */
    base::Result<void> SendType3Message(uint8_t target);

    /**
     * @brief 发送Type4消息（本地状态查询）
     * @param target 目标地址
     * @return 发送结果
     */
    base::Result<void> SendType4Message(uint8_t target);

    /**
     * @brief 发送Type5消息（重命名请求）
     * @param target 目标地址
     * @param new_name 新名称
     * @return 发送结果
     */
    base::Result<void> SendType5Message(uint8_t target, const std::string& new_name);

    /**
     * @brief 设置连接状态回调
     * @param callback 回调函数
     */
    void SetConnectionCallback(ConnectionCallback callback);

    /**
     * @brief 设置消息接收回调
     * @param callback 回调函数
     */
    void SetMessageCallback(MessageCallback callback);

private:
    // 配置信息
    std::string server_host_;
    uint16_t server_port_;

    // 网络组件
    std::unique_ptr<event::EventLoopThread> event_loop_thread_;
    event::EventLoop* event_loop_;
    std::unique_ptr<event::TcpClient> tcp_client_;
    event::TcpConnectionPtr connection_;

    // 状态管理
    std::atomic<bool> is_running_{false};
    std::atomic<bool> is_connected_{false};

    // 回调函数
    std::mutex callback_mutex_;
    ConnectionCallback connection_callback_;
    MessageCallback message_callback_;

    // 消息处理缓冲区
    std::string receive_buffer_;
    std::mutex buffer_mutex_;

    // 消息处理器工厂（保留handlers路由机制）
    std::unique_ptr<MessageTypeHandlerFactory> handler_factory_;

    // 私有方法
    void OnConnection(const event::TcpConnectionPtr& conn);
    void OnMessage(const event::TcpConnectionPtr& conn,
                  event::Buffer* buf,
                  std::chrono::system_clock::time_point receive_time);
    void OnWriteComplete(const event::TcpConnectionPtr& conn);

    // 消息处理
    void ProcessReceivedData(const std::string& data);
    bool ParseMessage(const std::vector<uint8_t>& frame_data, base::Message& message);

    // 消息发送辅助方法（通过handlers路由）
    base::Result<void> SendMessageViaHandler(uint8_t message_type, uint8_t target,
                                            const std::string& text_content = "",
                                            const std::vector<uint8_t>& variable_data = {});

    // Handlers管理
    void InitializeHandlers();
    IMessageTypeHandler* GetHandler(uint8_t message_type);

    // 回调通知
    void NotifyConnectionStatus(bool connected);
    void NotifyMessageReceived(const base::Message& message);
};

} // namespace client
} // namespace protocol
} // namespace zexuan

#endif // ZEXUAN_PROTOCOL_CLIENT_HPP
