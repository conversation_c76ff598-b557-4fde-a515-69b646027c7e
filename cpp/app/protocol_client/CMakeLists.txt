# 创建可执行文件
add_executable(protocol_client main.cpp)

# 设置可执行文件输出目录
set_target_properties(protocol_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置包含目录
target_include_directories(protocol_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)

# 链接库 - 只链接需要的库，避免protocol_server的编译问题
target_link_libraries(protocol_client
    PRIVATE
        zexuan_base
        zexuan_utils
        zexuan_event
        zexuan_protocol
        spdlog::spdlog
)