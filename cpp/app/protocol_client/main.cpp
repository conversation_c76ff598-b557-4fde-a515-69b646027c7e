/**
 * @file main.cpp
 * @brief IEC 60870-5-103 协议客户端主程序（现代化重构版本）
 * <AUTHOR>
 * @date 2024
 */

#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <spdlog/spdlog.h>
#include <unistd.h>

#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"

using namespace zexuan::protocol::client;

// 全局客户端实例
std::unique_ptr<ProtocolClient> g_client;
std::atomic<bool> g_running{true};
std::atomic<bool> g_connected{false};

// 消息发送计数器
std::atomic<int> g_message_counter{0};

/**
 * @brief 连接状态变化回调
 */
void onConnectionStateChanged(bool connected) {
    if (connected) {
        spdlog::info("LocalClient -> Server is UP");
        g_connected = true;
        spdlog::info("Protocol test client connected, will send 103 test data automatically");
    } else {
        spdlog::info("LocalClient -> Server is DOWN");
        g_connected = false;
    }
}

/**
 * @brief 消息接收回调
 */
void onMessageReceived(const zexuan::base::Message& message) {
    spdlog::info("Received message: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                message.getTyp(), message.getVsq(), message.getCot(),
                message.getSource(), message.getTarget(), message.getFun(), message.getInf());

    // 如果有文本内容，也打印出来
    if (!message.getTextContent().empty()) {
        spdlog::info("Message text content: {}", message.getTextContent());
    }
}

/**
 * @brief 消息发送定时器线程（每2秒发送一次，按顺序1,2,4,5）
 */
void messageTimerThread() {
    while (g_running && g_client) {
        if (g_connected && g_client->IsConnected()) {
            int message_type_index = (g_message_counter++ % 4);
            uint8_t message_type;
            
            switch (message_type_index) {
                case 0: message_type = 1; break; // Type 1
                case 1: message_type = 2; break; // Type 2  
                case 2: message_type = 4; break; // Type 4
                case 3: message_type = 5; break; // Type 5
                default: message_type = 1; break;
            }
            
            // 使用新的简化API发送消息
            zexuan::base::Result<void> result;

            switch (message_type) {
                case 1:
                    result = g_client->SendType1Message(9, "test data");
                    break;
                case 2:
                    result = g_client->SendType2Message(9, "multi frame test");
                    break;
                case 4:
                    result = g_client->SendType4Message(9);
                    break;
                case 5:
                    result = g_client->SendType5Message(10, "./files/");
                    break;
                default:
                    spdlog::error("Unknown message type: {}", message_type);
                    continue;
            }

            if (!result) {
                spdlog::error("Failed to send message type {}", message_type);
            }
        }
        
        // 等待2秒，但要检查是否需要退出
        for (int i = 0; i < 20 && g_running; ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
}

int main(int argc, char* argv[]) {
    spdlog::info("pid = {}", getpid());
    
    if (argc > 2) {
        try {
            // 创建协议客户端（使用命令行参数）
            std::string host = argv[1];
            uint16_t port = static_cast<uint16_t>(std::stoi(argv[2]));
            g_client = std::make_unique<ProtocolClient>(host, port);

            // 设置回调函数
            g_client->SetConnectionCallback(onConnectionStateChanged);
            g_client->SetMessageCallback(onMessageReceived);

            // 启动客户端
            auto start_result = g_client->Start();
            if (!start_result) {
                spdlog::error("Failed to start protocol client");
                return 1;
            }

            // 连接到服务器
            spdlog::info("Connecting to server {}:{}", host, port);

            auto connect_result = g_client->Connect();
            if (!connect_result) {
                spdlog::error("Failed to connect to server");
                return 1;
            }
            
            // 启动消息发送定时器线程
            std::thread timer_thread(messageTimerThread);
            
            // 等待一段时间让数据传输完成，然后可以手动输入更多数据
            spdlog::info("Waiting for response... You can also input additional messages:");
            
            std::string line;
            while (std::getline(std::cin, line) && g_running) {
                if (line == "quit" || line == "exit") {
                    break;
                }
                
                // 简单的消息发送（发送Type1消息）
                if (g_client && g_connected) {
                    auto result = g_client->SendType1Message(9, line);
                    if (result) {
                        printf(">>> Sent Type1 message: %s\n", line.c_str());
                    } else {
                        printf(">>> Failed to send message: %s\n", line.c_str());
                    }
                }
            }
            
            // 优雅关闭
            g_running = false;
            if (timer_thread.joinable()) {
                timer_thread.join();
            }
            
            if (g_client) {
                auto stop_result = g_client->Stop();
                if (!stop_result) {
                    spdlog::warn("Failed to stop client gracefully");
                }
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));  // wait for disconnect
            
        } catch (const std::exception& e) {
            spdlog::error("Exception in main: {}", e.what());
            return 1;
        }
    } else {
        printf("Usage: %s host_ip port\n", argv[0]);
    }
    
    return 0;
}