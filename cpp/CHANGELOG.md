# Changelog

## [1.0.5] - 2025-08-29
### Added
- protocol_client
    - 增加了protocol_client

## [1.0.4] - 2025-08-26
### Added
- rename_client

- lifecycle_component
    - 增加生命周期接口基类，所有复杂类都要使用继承这个基类并完善Start,Stop接口

### Changed
- controller、executor、mediator
    - 重命名 observer、subject为controller、executor

### Fixed
- protocol
    - 修复service中eventloop析构不在同一个线程的bug

## [1.0.3] - 2025-08-25
### Added
- bus
    - 现在注册可以带上clientid，可以通过clientid去寻找连接了

- protocol
    - protocol_service现在会自动设置sourceid，这里需要注意后续会不会用到sourceid

- eventloop
    - 增加定时器，和信号处理，不需要sleep去做定时时间了，信号也统一管理

- tcp_bus_client
    - 增加公用基类，减少耦合度
### Fixed
- protocol
    - protocol_gateway之前会自动修改commonmessage的targetid，删掉了，逻辑错误，只需要设置invokeid

- connector
    - 改成使用定时器而不是sleep重连

## [1.0.2] - 2025-08-24
### Added
- bus
    - 增加了bus层，内部使用protocol、router、serializer、manager、server、client层

- protocol
    - protocol_service增加单独总线客户端线程

### Deleted
- protocol
    - protocol_service所有的消息生成全部删除，逻辑全部移植到tcp_bus_client中

### Fixed
- tcp_connector
    - 修复自动重连的问题

## [1.0.1] - 2025-08-22
### Added
- event
    - address、socket、socket_ops等底层架构增加了对Unix Domain Socket的支持
    - 增加了uds_acceptor、uds_connector、uds_connection、uds_server、uds_client等高层组件

### Fixed
- protocol
    - service层没有对obs-sub进行exit()，导致shared无法析构

## [1.0.0] - 2025-08-21
### Added
- base
    - obs-med-sub架构
    - 消息模型
    - 各种常用基类
- utils
    - uuid封装
    - invokeid、string_uuid、file_utils封装
- event
    - 实现事件循环与网络栈
- protocol
    - 实现网络栈103规约到本地信息的转换，分server、gateway、transform、service四层
