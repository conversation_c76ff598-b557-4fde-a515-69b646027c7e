/**
 * @file message_handler_factory.cpp
 * @brief 简化的消息处理器工厂实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client/message_handler_factory.hpp"
#include "zexuan/protocol/protocol_client/handlers/type1_handler.hpp"
#include "zexuan/protocol/protocol_client/handlers/type2_handler.hpp"
#include "zexuan/protocol/protocol_client/handlers/type3_handler.hpp"
#include "zexuan/protocol/protocol_client/handlers/type4_handler.hpp"
#include "zexuan/protocol/protocol_client/handlers/type5_handler.hpp"
#include <spdlog/spdlog.h>

namespace zexuan {
namespace protocol {
namespace client {

MessageTypeHandlerFactory::MessageTypeHandlerFactory() {
    InitializeDefaultHandlers();
}

IMessageTypeHandler* MessageTypeHandlerFactory::GetHandler(uint8_t message_type) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = handlers_.find(message_type);
    return (it != handlers_.end()) ? it->second.get() : nullptr;
}



void MessageTypeHandlerFactory::InitializeDefaultHandlers() {
    spdlog::info("MessageTypeHandlerFactory: Initializing default handlers");

    // 创建所有handlers
    auto type1_handler = std::make_unique<Type1Handler>();
    auto type2_handler = std::make_unique<Type2Handler>();
    auto type3_handler = std::make_unique<Type3Handler>();
    auto type4_handler = std::make_unique<Type4Handler>();
    auto type5_handler = std::make_unique<Type5Handler>();

    // 注册handlers
    handlers_[1] = std::move(type1_handler);
    handlers_[2] = std::move(type2_handler);
    handlers_[3] = std::move(type3_handler);
    handlers_[4] = std::move(type4_handler);
    handlers_[5] = std::move(type5_handler);

    spdlog::info("MessageTypeHandlerFactory: Initialized {} default handlers", handlers_.size());
}

} // namespace client
} // namespace protocol
} // namespace zexuan
