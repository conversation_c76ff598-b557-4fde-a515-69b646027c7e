/**
 * @file protocol_client.cpp
 * @brief 简化的IEC 60870-5-103协议客户端实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"
#include "zexuan/event/event_loop_thread.hpp"

// 包含factory和handlers接口
#include "zexuan/protocol/protocol_client/client/message_handler_factory.hpp"
#include "zexuan/protocol/protocol_client/client/message_handler_interface.hpp"

#include <spdlog/spdlog.h>
#include <chrono>

namespace zexuan {
namespace protocol {
namespace client {

ProtocolClient::ProtocolClient(const std::string& server_host, uint16_t server_port)
    : server_host_(server_host)
    , server_port_(server_port)
    , event_loop_thread_(nullptr)
    , event_loop_(nullptr)
    , tcp_client_(nullptr) {
    
    // 初始化handler factory
    InitializeHandlers();
    
    spdlog::info("ProtocolClient: Created for {}:{}", server_host_, server_port_);
}

ProtocolClient::~ProtocolClient() {
    if (is_running_.load()) {
        auto result = Stop();
        if (!result) {
            spdlog::error("ProtocolClient: Failed to stop in destructor");
        }
    }
    spdlog::info("ProtocolClient: Destroyed");
}

base::Result<void> ProtocolClient::Start() {
    if (is_running_.load()) {
        spdlog::warn("ProtocolClient: Already running");
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    try {
        // 创建事件循环线程
        event_loop_thread_ = std::make_unique<event::EventLoopThread>();
        event_loop_ = event_loop_thread_->startLoop();
        
        if (!event_loop_) {
            spdlog::error("ProtocolClient: Failed to start event loop");
            return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        // 创建服务器地址
        event::Address server_addr(server_host_, server_port_);

        // 创建TCP客户端
        tcp_client_ = std::make_unique<event::TcpClient>(event_loop_, server_addr, "ProtocolClient");

        // 设置回调
        tcp_client_->setConnectionCallback(
            [this](const event::TcpConnectionPtr& conn) {
                OnConnection(conn);
            });

        tcp_client_->setMessageCallback(
            [this](const event::TcpConnectionPtr& conn, event::Buffer* buf,
                   std::chrono::system_clock::time_point receive_time) {
                OnMessage(conn, buf, receive_time);
            });

        tcp_client_->setWriteCompleteCallback(
            [this](const event::TcpConnectionPtr& conn) {
                OnWriteComplete(conn);
            });

        is_running_.store(true);
        spdlog::info("ProtocolClient: Started successfully");
        return {};

    } catch (const std::exception& e) {
        spdlog::error("ProtocolClient: Start failed: {}", e.what());
        return std::unexpected(base::ErrorCode::OPERATION_FAILED);
    }
}

base::Result<void> ProtocolClient::Stop() {
    if (!is_running_.load()) {
        spdlog::warn("ProtocolClient: Not running");
        return {};
    }

    try {
        // 断开连接
        Disconnect();

        // 停止TCP客户端
        if (tcp_client_) {
            tcp_client_.reset();
        }

        // 停止事件循环
        if (event_loop_thread_) {
            event_loop_thread_.reset();
            event_loop_ = nullptr;
        }

        is_running_.store(false);
        spdlog::info("ProtocolClient: Stopped successfully");
        return {};

    } catch (const std::exception& e) {
        spdlog::error("ProtocolClient: Stop failed: {}", e.what());
        return std::unexpected(base::ErrorCode::OPERATION_FAILED);
    }
}

base::Result<void> ProtocolClient::Connect() {
    if (!is_running_.load()) {
        spdlog::error("ProtocolClient: Not started");
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    if (is_connected_.load()) {
        spdlog::warn("ProtocolClient: Already connected");
        return {};
    }

    if (!tcp_client_) {
        spdlog::error("ProtocolClient: TCP client not initialized");
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    try {
        tcp_client_->connect();
        spdlog::info("ProtocolClient: Connection initiated to {}:{}", server_host_, server_port_);
        return {};

    } catch (const std::exception& e) {
        spdlog::error("ProtocolClient: Connect failed: {}", e.what());
        return std::unexpected(base::ErrorCode::OPERATION_FAILED);
    }
}

void ProtocolClient::Disconnect() {
    if (connection_) {
        connection_->shutdown();
        connection_.reset();
    }
    is_connected_.store(false);
    spdlog::info("ProtocolClient: Disconnected");
}

bool ProtocolClient::IsConnected() const {
    return is_connected_.load();
}

base::Result<void> ProtocolClient::SendType1Message(uint8_t target, const std::string& test_data) {
    return SendMessageViaHandler(1, target, test_data);
}

base::Result<void> ProtocolClient::SendType2Message(uint8_t target, const std::string& test_data) {
    return SendMessageViaHandler(2, target, test_data);
}

base::Result<void> ProtocolClient::SendType3Message(uint8_t target) {
    return SendMessageViaHandler(3, target);
}

base::Result<void> ProtocolClient::SendType4Message(uint8_t target) {
    return SendMessageViaHandler(4, target);
}

base::Result<void> ProtocolClient::SendType5Message(uint8_t target, const std::string& new_name) {
    return SendMessageViaHandler(5, target, new_name);
}

void ProtocolClient::SetConnectionCallback(ConnectionCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    connection_callback_ = std::move(callback);
}

void ProtocolClient::SetMessageCallback(MessageCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    message_callback_ = std::move(callback);
}

// 私有方法实现
void ProtocolClient::OnConnection(const event::TcpConnectionPtr& conn) {
    if (conn->connected()) {
        connection_ = conn;
        is_connected_.store(true);
        spdlog::info("ProtocolClient: Connected to {}:{}", server_host_, server_port_);
        NotifyConnectionStatus(true);
    } else {
        connection_.reset();
        is_connected_.store(false);
        spdlog::info("ProtocolClient: Disconnected from {}:{}", server_host_, server_port_);
        NotifyConnectionStatus(false);
    }
}

void ProtocolClient::OnMessage(const event::TcpConnectionPtr& conn, 
                              event::Buffer* buf, 
                              std::chrono::system_clock::time_point receive_time) {
    std::string data = buf->retrieveAllAsString();
    spdlog::debug("ProtocolClient: Received {} bytes", data.size());
    ProcessReceivedData(data);
}

void ProtocolClient::OnWriteComplete(const event::TcpConnectionPtr& conn) {
    spdlog::debug("ProtocolClient: Write completed");
}

void ProtocolClient::InitializeHandlers() {
    // 创建handler factory
    handler_factory_ = std::make_unique<MessageTypeHandlerFactory>();

    spdlog::info("ProtocolClient: Initialized message handler factory");
}

IMessageTypeHandler* ProtocolClient::GetHandler(uint8_t message_type) {
    if (!handler_factory_) {
        return nullptr;
    }
    return handler_factory_->GetHandler(message_type);
}

void ProtocolClient::NotifyConnectionStatus(bool connected) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (connection_callback_) {
        connection_callback_(connected);
    }
}

void ProtocolClient::NotifyMessageReceived(const base::Message& message) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (message_callback_) {
        message_callback_(message);
    }
}

base::Result<void> ProtocolClient::SendMessageViaHandler(uint8_t message_type, uint8_t target,
                                                        const std::string& text_content,
                                                        const std::vector<uint8_t>& variable_data) {
    if (!is_connected_.load()) {
        spdlog::error("ProtocolClient: Not connected");
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    // 获取对应的handler
    auto* handler = GetHandler(message_type);
    if (!handler) {
        spdlog::error("ProtocolClient: No handler for message type {}", message_type);
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    // 创建输入消息对象
    base::Message input_message;
    input_message.setTyp(message_type);  // 设置消息类型
    input_message.setTarget(target);
    if (!text_content.empty()) {
        input_message.setTextContent(text_content);
    }
    if (!variable_data.empty()) {
        input_message.setVariableStructure(variable_data);
    }

    // 通过handler创建消息
    auto message_result = handler->CreateMessage(input_message);
    if (!message_result) {
        spdlog::error("ProtocolClient: Failed to create message type {}", message_type);
        return std::unexpected(message_result.error());
    }

    // 序列化消息
    std::vector<uint8_t> serialized_data;
    size_t serialized_size = message_result.value().serialize(serialized_data);

    if (serialized_size == 0) {
        spdlog::error("ProtocolClient: Failed to serialize message type {}", message_type);
        return std::unexpected(base::ErrorCode::OPERATION_FAILED);
    }

    // 发送数据
    if (!connection_) {
        spdlog::error("ProtocolClient: No active connection");
        return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
    }

    connection_->send(serialized_data.data(), serialized_size);
    spdlog::info("ProtocolClient: Sent message type {} to target {}", message_type, target);

    return {};
}

void ProtocolClient::ProcessReceivedData(const std::string& data) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    receive_buffer_ += data;

    // 简化的帧解析 - 寻找完整的IEC103消息
    while (receive_buffer_.size() >= 6) { // 最小消息长度
        // 寻找起始字符
        size_t start_pos = receive_buffer_.find('\x68');
        if (start_pos == std::string::npos) {
            receive_buffer_.clear();
            break;
        }

        if (start_pos > 0) {
            receive_buffer_.erase(0, start_pos);
        }

        if (receive_buffer_.size() < 6) break;

        // 获取长度字段
        uint8_t length = static_cast<uint8_t>(receive_buffer_[1]);
        size_t total_length = length + 6; // 长度 + 固定开销

        if (receive_buffer_.size() < total_length) {
            break; // 数据不完整
        }

        // 提取完整帧
        std::vector<uint8_t> frame_data(receive_buffer_.begin(),
                                       receive_buffer_.begin() + total_length);
        receive_buffer_.erase(0, total_length);

        // 解析消息
        base::Message message;
        if (ParseMessage(frame_data, message)) {
            NotifyMessageReceived(message);
        }
    }
}

bool ProtocolClient::ParseMessage(const std::vector<uint8_t>& frame_data, base::Message& message) {
    // 使用base::Message的deserialize方法
    size_t parsed = message.deserialize(frame_data);

    if (parsed > 0) {
        spdlog::debug("ProtocolClient: Parsed IEC103 message: TYP={:02X}, VSQ={:02X}, COT={:02X}, "
                     "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
                     message.getTyp(), message.getVsq(), message.getCot(),
                     message.getSource(), message.getTarget(), message.getFun(), message.getInf());
        return true;
    } else {
        spdlog::warn("ProtocolClient: Failed to parse message frame");
        return false;
    }
}

} // namespace client
} // namespace protocol
} // namespace zexuan
